In lib/supabase.ts at lines 173, 211, and 249, the readiness_promotion field is currently typed as string | null, but it should be a union type of the specific allowed string literals "strong-yes", "yes-with-reservations", "no-not-yet" or null. Update the type definitions for readiness_promotion in the Row, Insert, and Update interfaces to use this union type for improved type safety and consistency with the form component.
In lib/schemas.ts around lines 234 to 243, the error messages for the .max(5) validations on rating fields incorrectly state the field is required. Update these messages to clearly indicate that the value must be at most 5, reflecting the maximum value constraint instead of a required field error.
In lib/actions/search.ts at line 65, remove the console.log statement used for debug logging or wrap it in a condition that checks if the environment is not production before logging. This ensures debug logs do not appear in production, keeping the output clean and secure.
In lib/actions/search.ts around lines 37 to 46, the code contains multiple console.log statements used for debugging. Remove these console.log calls or wrap them in a condition that checks if the environment is development to prevent debug logs from appearing in production. Alternatively, replace console.log with a proper logging library that supports log levels and environment-based logging control.
In lib/supabase.ts at lines 173, 211, and 249, the readiness_promotion field is currently typed as string | null, but it should be a union type of the specific allowed string literals "strong-yes", "yes-with-reservations", "no-not-yet" or null. Update the type definitions for readiness_promotion in the Row, Insert, and Update interfaces to use this union type for improved type safety and consistency with the form component.
In lib/actions/employees.ts around lines 84 to 85, the soft delete functionality is currently missing and only noted as a TODO. Implement the soft delete by creating or using a function that updates the employee's record to set the `active` field to `false` instead of deleting the record. Replace the commented-out placeholder with a call to this function, passing the employeeId to mark the employee as inactive.
In lib/actions/pto.ts around lines 45 to 52, the code incorrectly finds employees managed by the current user instead of the current user's own employee record. Update the lookup to find the employee record that matches the current user's ID, such as by using a user_id field in the employees table or another appropriate identifier that directly links the employee record to the current user.
