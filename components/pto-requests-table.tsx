"use client"

import { useState } from "react"
import { toast } from "sonner"
import { formatDistanceToNow } from "date-fns"
import {
  Check,
  X,
  Clock,
  Calendar,
  User,
  AlertCircle,
  MoreHorizontal,
  Eye,
  XCircle,
  CheckCircle,
  ClockIcon,
} from "lucide-react"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import { approvePTORequestAction, cancelPTORequestAction } from "@/lib/actions/pto"
import type { PTORequest, PTORequestStatus, PTORequestType } from "@/lib/types"

interface PTORequestsTableProps {
  requests: PTORequest[]
  viewMode?: 'manager' | 'employee'
  onRequestUpdate?: () => void
  title?: string
  description?: string
  emptyMessage?: string
}

const statusConfig = {
  pending: {
    label: 'Pending',
    icon: Clock,
    className: 'bg-yellow-100 text-yellow-800',
  },
  approved: {
    label: 'Approved',
    icon: CheckCircle,
    className: 'bg-green-100 text-green-800',
  },
  rejected: {
    label: 'Rejected',
    icon: XCircle,
    className: 'bg-red-100 text-red-800',
  },
  cancelled: {
    label: 'Cancelled',
    icon: AlertCircle,
    className: 'bg-gray-100 text-gray-800',
  },
}

const requestTypeConfig = {
  vacation: { label: 'Vacation', className: 'bg-blue-100 text-blue-800' },
  sick: { label: 'Sick Leave', className: 'bg-orange-100 text-orange-800' },
  personal: { label: 'Personal Day', className: 'bg-purple-100 text-purple-800' },
  emergency: { label: 'Emergency', className: 'bg-red-100 text-red-800' },
}

export function PTORequestsTable({
  requests,
  viewMode = 'employee',
  onRequestUpdate,
  title = 'PTO Requests',
  description,
  emptyMessage = 'No PTO requests found.',
}: PTORequestsTableProps) {
  const [selectedRequest, setSelectedRequest] = useState<PTORequest | null>(null)
  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = useState(false)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [rejectionReason, setRejectionReason] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleApproval = async (requestId: string, action: 'approve' | 'reject') => {
    setIsSubmitting(true)
    
    try {
      const result = await approvePTORequestAction(
        requestId,
        action,
        action === 'reject' ? rejectionReason : undefined
      )

      if (result.success) {
        toast.success('message' in result ? result.message : `PTO request ${action}d successfully`)
        setIsApprovalDialogOpen(false)
        setRejectionReason('')
        onRequestUpdate?.()
      } else {
        toast.error('error' in result ? result.error : `Failed to ${action} PTO request`)
      }
    } catch (error) {
      // console.error(`Error ${action}ing PTO request:`, error)
      toast.error('An unexpected error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancelRequest = async (requestId: string) => {
    setIsSubmitting(true)
    
    try {
      const result = await cancelPTORequestAction(requestId)

      if (result.success) {
        toast.success('message' in result ? result.message : 'PTO request cancelled successfully')
        onRequestUpdate?.()
      } else {
        toast.error('error' in result ? result.error : 'Failed to cancel PTO request')
      }
    } catch (error) {
      // console.error('Error cancelling PTO request:', error)
      toast.error('An unexpected error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    const startStr = start.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })
    const endStr = end.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    })
    
    return `${startStr} - ${endStr}`
  }

  const StatusBadge = ({ status }: { status: PTORequestStatus }) => {
    const config = statusConfig[status]
    const Icon = config.icon
    
    return (
      <Badge className={config.className}>
        <Icon className="mr-1 h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  const RequestTypeBadge = ({ type }: { type: PTORequestType }) => {
    const config = requestTypeConfig[type]
    
    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {requests.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500 text-sm">{emptyMessage}</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                {viewMode === 'manager' && <TableHead>Employee</TableHead>}
                <TableHead>Type</TableHead>
                <TableHead>Dates</TableHead>
                <TableHead>Days</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {requests.map((request) => (
                <TableRow key={request.id}>
                  {viewMode === 'manager' && (
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="font-medium">{request.employeeName}</span>
                      </div>
                    </TableCell>
                  )}
                  <TableCell>
                    <RequestTypeBadge type={request.requestType} />
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDateRange(request.startDate, request.endDate)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm font-medium">{request.daysRequested}</div>
                  </TableCell>
                  <TableCell>
                    <StatusBadge status={request.status} />
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-gray-500">
                      {formatDistanceToNow(new Date(request.createdAt), { addSuffix: true })}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedRequest(request)
                            setIsDetailDialogOpen(true)
                          }}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        
                        {viewMode === 'manager' && request.status === 'pending' && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedRequest(request)
                                setIsApprovalDialogOpen(true)
                              }}
                            >
                              <Check className="mr-2 h-4 w-4" />
                              Approve/Reject
                            </DropdownMenuItem>
                          </>
                        )}
                        
                        {viewMode === 'employee' && request.status === 'pending' && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleCancelRequest(request.id)}
                              disabled={isSubmitting}
                            >
                              <X className="mr-2 h-4 w-4" />
                              Cancel Request
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* Request Details Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>PTO Request Details</DialogTitle>
            <DialogDescription>
              {selectedRequest && `Request submitted by ${selectedRequest.employeeName}`}
            </DialogDescription>
          </DialogHeader>
          {selectedRequest && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Type</Label>
                  <div className="mt-1">
                    <RequestTypeBadge type={selectedRequest.requestType} />
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <div className="mt-1">
                    <StatusBadge status={selectedRequest.status} />
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Start Date</Label>
                  <div className="mt-1 text-sm">
                    {new Date(selectedRequest.startDate).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">End Date</Label>
                  <div className="mt-1 text-sm">
                    {new Date(selectedRequest.endDate).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium">Days Requested</Label>
                <div className="mt-1 text-sm font-medium">{selectedRequest.daysRequested} days</div>
              </div>
              
              {selectedRequest.reason && (
                <div>
                  <Label className="text-sm font-medium">Reason</Label>
                  <div className="mt-1 text-sm bg-gray-50 p-3 rounded-lg">
                    {selectedRequest.reason}
                  </div>
                </div>
              )}
              
              <div>
                <Label className="text-sm font-medium">Submitted</Label>
                <div className="mt-1 text-sm text-gray-500">
                  {new Date(selectedRequest.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>
              
              {selectedRequest.status === 'approved' && selectedRequest.approvedAt && (
                <div>
                  <Label className="text-sm font-medium">Approved</Label>
                  <div className="mt-1 text-sm text-green-600">
                    {new Date(selectedRequest.approvedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
              )}
              
              {selectedRequest.status === 'rejected' && selectedRequest.rejectedReason && (
                <div>
                  <Label className="text-sm font-medium">Rejection Reason</Label>
                  <div className="mt-1 text-sm bg-red-50 p-3 rounded-lg text-red-700">
                    {selectedRequest.rejectedReason}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Approval Dialog */}
      <Dialog open={isApprovalDialogOpen} onOpenChange={setIsApprovalDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Approve PTO Request</DialogTitle>
            <DialogDescription>
              {selectedRequest && `Review ${selectedRequest.employeeName}'s PTO request`}
            </DialogDescription>
          </DialogHeader>
          {selectedRequest && (
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Type:</strong> <RequestTypeBadge type={selectedRequest.requestType} />
                  </div>
                  <div>
                    <strong>Days:</strong> {selectedRequest.daysRequested}
                  </div>
                  <div className="col-span-2">
                    <strong>Dates:</strong> {formatDateRange(selectedRequest.startDate, selectedRequest.endDate)}
                  </div>
                  {selectedRequest.reason && (
                    <div className="col-span-2">
                      <strong>Reason:</strong> {selectedRequest.reason}
                    </div>
                  )}
                </div>
              </div>
              
              <div>
                <Label htmlFor="rejection-reason">Rejection Reason (if rejecting)</Label>
                <Textarea
                  id="rejection-reason"
                  placeholder="Enter reason for rejection..."
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsApprovalDialogOpen(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => handleApproval(selectedRequest.id, 'reject')}
                  disabled={isSubmitting || !rejectionReason.trim()}
                >
                  {isSubmitting ? (
                    <>
                      <ClockIcon className="mr-2 h-4 w-4 animate-spin" />
                      Rejecting...
                    </>
                  ) : (
                    <>
                      <X className="mr-2 h-4 w-4" />
                      Reject
                    </>
                  )}
                </Button>
                <Button
                  onClick={() => handleApproval(selectedRequest.id, 'approve')}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <ClockIcon className="mr-2 h-4 w-4 animate-spin" />
                      Approving...
                    </>
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Approve
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  )
}