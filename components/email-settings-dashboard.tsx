'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { AlertCircle, CheckCircle, Mail, Settings, TestTube, Clock, Loader2 } from 'lucide-react'
import { toast } from 'sonner'
import { AuthUser } from '@/lib/auth'
import { EmailBasicSettings } from './email-basic-settings'
import { EmailTemplateEditor } from './email-template-editor'
import { EmailScheduleSettings } from './email-schedule-settings'
import { EmailTestPanel } from './email-test-panel'

interface EmailSettingsData {
  settings: Record<string, any>
  templates: any[]
  systemStatus: {
    configured: boolean
    lastSent?: string
    recentFailures: number
    queuedNotifications: number
  }
}

interface Props {
  currentUser: AuthUser
}

export function EmailSettingsDashboard({ currentUser }: Props) {
  const [data, setData] = useState<EmailSettingsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('basic')

  // Load email settings data
  useEffect(() => {
    fetchEmailSettings()
  }, [])

  const fetchEmailSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/email-settings')
      
      if (!response.ok) {
        throw new Error('Failed to fetch email settings')
      }
      
      const result = await response.json()
      setData(result.data)
    } catch (error) {
      console.error('Error fetching email settings:', error)
      toast.error('Failed to load email settings')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveSettings = async (updates: { settings?: any; templates?: any[] }) => {
    try {
      setSaving(true)
      
      const response = await fetch('/api/admin/email-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates)
      })
      
      const result = await response.json()
      
      if (result.success) {
        toast.success('Email settings updated successfully')
        await fetchEmailSettings() // Refresh data
      } else {
        throw new Error(result.error || 'Failed to update settings')
      }
    } catch (error) {
      console.error('Error saving email settings:', error)
      toast.error('Failed to save email settings')
    } finally {
      setSaving(false)
    }
  }

  const handleTestEmail = async () => {
    try {
      const response = await fetch('/api/admin/email-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'test' })
      })
      
      const result = await response.json()
      
      if (result.success) {
        toast.success('Test email sent successfully')
      } else {
        throw new Error(result.error || 'Failed to send test email')
      }
    } catch (error) {
      console.error('Error sending test email:', error)
      toast.error('Failed to send test email')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>Loading email settings...</span>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
          <p className="text-muted-foreground">Failed to load email settings</p>
          <Button onClick={fetchEmailSettings} className="mt-4">
            Retry
          </Button>
        </div>
      </div>
    )
  }

  const { settings, templates, systemStatus } = data

  return (
    <div className="space-y-6">
      {/* System Status Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email System Status
          </CardTitle>
          <CardDescription>
            Current status and health of the email notification system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                {systemStatus.configured ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                )}
                <span className="text-sm font-medium">
                  {systemStatus.configured ? 'Configured' : 'Needs Setup'}
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <div className="text-sm">
                <span className="font-medium">Last Sent:</span>
                <br />
                <span className="text-muted-foreground">
                  {systemStatus.lastSent 
                    ? new Date(systemStatus.lastSent).toLocaleString()
                    : 'Never'
                  }
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="text-sm">
                <span className="font-medium">Recent Failures:</span>
                <br />
                <Badge variant={systemStatus.recentFailures > 0 ? 'destructive' : 'secondary'}>
                  {systemStatus.recentFailures}
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="text-sm">
                <span className="font-medium">Queued:</span>
                <br />
                <Badge variant={systemStatus.queuedNotifications > 0 ? 'default' : 'secondary'}>
                  {systemStatus.queuedNotifications}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Settings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Settings</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="schedule">Scheduling</TabsTrigger>
          <TabsTrigger value="test">Testing</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <EmailBasicSettings 
            settings={settings}
            onSave={(settingsUpdate) => handleSaveSettings({ settings: settingsUpdate })}
            saving={saving}
          />
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <EmailTemplateEditor 
            templates={templates}
            settings={settings}
            onSave={(templatesUpdate) => handleSaveSettings({ templates: templatesUpdate })}
            saving={saving}
          />
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <EmailScheduleSettings 
            settings={settings}
            onSave={(settingsUpdate) => handleSaveSettings({ settings: settingsUpdate })}
            saving={saving}
          />
        </TabsContent>

        <TabsContent value="test" className="space-y-4">
          <EmailTestPanel 
            settings={settings}
            templates={templates}
            systemStatus={systemStatus}
            onTestEmail={handleTestEmail}
            onRefresh={fetchEmailSettings}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}