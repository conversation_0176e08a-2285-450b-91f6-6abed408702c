/**
 * Transition utilities for smooth animations and state changes
 */

// Common transition classes for Tailwind CSS
export const transitions = {
  // Basic transitions
  default: "transition-all duration-200 ease-in-out",
  fast: "transition-all duration-150 ease-in-out",
  slow: "transition-all duration-300 ease-in-out",
  
  // Specific property transitions
  colors: "transition-colors duration-200 ease-in-out",
  opacity: "transition-opacity duration-200 ease-in-out",
  transform: "transition-transform duration-200 ease-in-out",
  
  // Component-specific transitions
  button: "transition-all duration-150 ease-in-out hover:scale-105 active:scale-95",
  card: "transition-all duration-200 ease-in-out hover:shadow-md",
  modal: "transition-all duration-200 ease-in-out",
  
  // Loading states
  fadeIn: "animate-in fade-in duration-200",
  fadeOut: "animate-out fade-out duration-200",
  slideIn: "animate-in slide-in-from-bottom-4 duration-200",
  slideOut: "animate-out slide-out-to-bottom-4 duration-200",
  
  // Focus states
  focus: "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-all duration-150",
  focusVisible: "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 transition-all duration-150"
} as const

// Animation variants for different states
export const animationVariants = {
  // Loading states
  loading: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 }
  },
  
  // Modal/Dialog states
  modal: {
    initial: { opacity: 0, scale: 0.95, y: 10 },
    animate: { opacity: 1, scale: 1, y: 0 },
    exit: { opacity: 0, scale: 0.95, y: 10 }
  },
  
  // Slide transitions
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
  },
  
  slideDown: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 20 }
  },
  
  // Fade transitions
  fade: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  },
  
  // Scale transitions
  scale: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 }
  }
} as const

// Stagger animations for lists
export const staggerVariants = {
  container: {
    initial: {},
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  },
  item: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  }
} as const

// Utility functions for programmatic animations
export function createTransition(
  duration: number = 200,
  easing: string = "ease-in-out",
  delay: number = 0
): string {
  return `transition-all duration-${duration} ${easing}${delay ? ` delay-${delay}` : ""}`
}

export function createKeyframes(name: string, keyframes: Record<string, Record<string, string>>): string {
  const keyframeString = Object.entries(keyframes)
    .map(([percentage, styles]) => {
      const styleString = Object.entries(styles)
        .map(([property, value]) => `${property}: ${value}`)
        .join('; ')
      return `${percentage} { ${styleString} }`
    })
    .join(' ')
  
  return `@keyframes ${name} { ${keyframeString} }`
}

// Predefined loading animations
export const loadingAnimations = {
  spin: "animate-spin",
  pulse: "animate-pulse",
  bounce: "animate-bounce",
  ping: "animate-ping",
  
  // Custom loading states
  fadeInOut: "animate-pulse",
  slideInOut: "animate-bounce",
  
  // Skeleton loading
  skeleton: "animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%]"
} as const

// Accessibility-friendly reduced motion variants
export const reducedMotionVariants = {
  // When user prefers reduced motion, use simpler transitions
  default: "motion-reduce:transition-none",
  fade: "motion-reduce:transition-opacity motion-reduce:duration-0",
  transform: "motion-reduce:transform-none",
  
  // Respect user preferences
  respectMotion: "motion-reduce:animate-none motion-reduce:transition-none"
} as const

// Utility to check if user prefers reduced motion
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false
  
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

// Enhanced transition classes that respect accessibility
export function getAccessibleTransition(
  baseTransition: string,
  reducedMotionFallback?: string
): string {
  const reduced = reducedMotionFallback || "motion-reduce:transition-none"
  return `${baseTransition} ${reduced}`
}

// State-based transition utilities
export const stateTransitions = {
  // Button states
  button: {
    idle: "bg-primary text-primary-foreground",
    hover: "bg-primary/90 scale-105",
    active: "bg-primary/80 scale-95",
    disabled: "bg-muted text-muted-foreground cursor-not-allowed",
    loading: "bg-primary/70 cursor-wait"
  },
  
  // Input states
  input: {
    idle: "border-input bg-background",
    focus: "border-ring ring-2 ring-ring/20",
    error: "border-destructive ring-2 ring-destructive/20",
    success: "border-green-500 ring-2 ring-green-500/20",
    disabled: "border-muted bg-muted cursor-not-allowed"
  },
  
  // Card states
  card: {
    idle: "bg-card border-border",
    hover: "bg-card/80 border-border/80 shadow-md",
    selected: "bg-accent border-accent-foreground/20",
    loading: "bg-muted/50 border-muted"
  }
} as const

// Timing functions
export const easingFunctions = {
  linear: "linear",
  easeIn: "ease-in",
  easeOut: "ease-out",
  easeInOut: "ease-in-out",
  
  // Custom cubic-bezier functions
  smooth: "cubic-bezier(0.4, 0, 0.2, 1)",
  snappy: "cubic-bezier(0.4, 0, 0.6, 1)",
  gentle: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
  
  // Bounce and elastic
  bounceOut: "cubic-bezier(0.34, 1.56, 0.64, 1)",
  elasticOut: "cubic-bezier(0.68, -0.55, 0.265, 1.55)"
} as const

// Duration presets (in milliseconds)
export const durations = {
  instant: 0,
  fast: 150,
  normal: 200,
  slow: 300,
  slower: 500,
  slowest: 1000
} as const

console.log('[A11Y] Transition utilities loaded with accessibility support')
