# Debug Logging

This application includes a comprehensive debug logging system that can be easily controlled.

## How to Control Debug Logs

### Environment Variables

Debug logging is controlled by the `ENABLE_DEBUG_LOGS` environment variable:

- `ENABLE_DEBUG_LOGS=true` - Enables debug logs (default in development)
- `ENABLE_DEBUG_LOGS=false` - Disables debug logs

### Default Behavior

- **Development mode** (`NODE_ENV=development`): Debug logs are enabled by default
- **Production mode** (`NODE_ENV=production`): Debug logs are disabled by default

### Configuration

Add to your `.env.local` file:

```bash
# Enable debug logs
ENABLE_DEBUG_LOGS=true

# Disable debug logs
ENABLE_DEBUG_LOGS=false
```

## Debug Utility Usage

The debug utility is located at `lib/debug.ts` and provides:

```typescript
import { debug } from '@/lib/debug'

// Debug logging (only shows when debug mode is enabled)
debug.log('Debug message', data)
debug.error('Error message', error)
debug.warn('Warning message')
debug.info('Info message')

// Check if debug mode is enabled
if (debug.isEnabled()) {
  // Expensive debug operations
}

// Conditional execution
debug.run(() => {
  // This only runs if debug mode is enabled
  console.log('Expensive debug operation')
})
```

## What Gets Logged

When debug mode is enabled, you'll see logs for:

- **Database Operations**: Employee fetching, manager lookups, role assignments
- **Authentication**: User sessions, role checks
- **Search**: FTS queries, result processing
- **Appraisals**: Form submissions, validation, status changes
- **API Calls**: Request/response data

## Performance Impact

Debug logs are completely disabled in production by default, so there's no performance impact when `ENABLE_DEBUG_LOGS=false`.

## Clean Terminal Output

To get clean terminal output without debug logs:

1. Set `ENABLE_DEBUG_LOGS=false` in your `.env.local`
2. Restart your development server
3. Debug logs will no longer appear in the terminal

## Example Debug Output

When enabled, you'll see logs like:

```
🔍 [DEBUG] getManagerAppraisals - Current user: { id: "user_123", fullName: "John Doe", role: "manager" }
👥 [DEBUG] getManagerAppraisals - All employees: [...]
🎯 [DEBUG] Employee Jane Smith is also a manager with role: manager
👤 [DEBUG] Employee Bob Johnson is a regular employee
🗺️ [DEBUG] Final roleMap: [["emp_1", "manager"], ["emp_2", "employee"]]
```
