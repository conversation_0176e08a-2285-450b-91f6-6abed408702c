import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { processApprovalStep, canUserApproveStep } from '@/lib/data/approvals'
import { z } from 'zod'

const processApprovalSchema = z.object({
  stepId: z.string().uuid(),
  action: z.enum(['approved', 'rejected']),
  comments: z.string().optional(),
  rejectionReason: z.string().optional()
})

/**
 * POST - Process approval step (approve or reject)
 */
export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = processApprovalSchema.parse(body)

    // Check if user can approve this step
    const canApprove = await canUserApproveStep(validatedData.stepId, currentUser.id)
    
    if (!canApprove) {
      return NextResponse.json({ 
        error: 'You do not have permission to approve this step' 
      }, { status: 403 })
    }

    // Validate rejection reason if rejecting
    if (validatedData.action === 'rejected' && !validatedData.rejectionReason?.trim()) {
      return NextResponse.json({ 
        error: 'Rejection reason is required when rejecting an appraisal' 
      }, { status: 400 })
    }

    // Process the approval step
    const result = await processApprovalStep(
      validatedData.stepId,
      validatedData.action,
      currentUser.id,
      currentUser.fullName,
      validatedData.comments,
      validatedData.rejectionReason
    )

    if (result.success) {
      return NextResponse.json({ 
        success: true, 
        message: `Appraisal ${validatedData.action} successfully` 
      })
    } else {
      return NextResponse.json({ 
        error: result.error || 'Failed to process approval' 
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error processing approval:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid input data',
        details: error.errors 
      }, { status: 400 })
    }

    return NextResponse.json({ 
      error: 'Failed to process approval' 
    }, { status: 500 })
  }
}
