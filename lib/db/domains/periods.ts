import { supabaseAdmin } from '../../supabase'
import type { AppraisalPeriod } from '../core/types'

// Appraisal Periods
export async function getAppraisalPeriods(): Promise<AppraisalPeriod[]> {
  const { data, error } = await supabaseAdmin.from('appy_appraisal_periods')
    .select('id, name, start_date, end_date, status, created_at')
    .order('start_date', { ascending: false })
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data || []
}

export async function createAppraisalPeriod(periodData: {
  name: string
  startDate: string
  endDate: string
  status?: string
}): Promise<AppraisalPeriod> {
  const { data, error } = await supabaseAdmin.from('appy_appraisal_periods')
    .insert({
      name: periodData.name,
      start_date: periodData.startDate,
      end_date: periodData.endDate,
      status: periodData.status || 'draft'
    })
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function updateAppraisalPeriod(id: string, periodData: {
  name?: string
  startDate?: string
  endDate?: string
  status?: string
}): Promise<AppraisalPeriod> {
  const updateData: any = {}
  
  if (periodData.name !== undefined) updateData.name = periodData.name
  if (periodData.startDate !== undefined) updateData.start_date = periodData.startDate
  if (periodData.endDate !== undefined) updateData.end_date = periodData.endDate
  if (periodData.status !== undefined) updateData.status = periodData.status
  
  if (Object.keys(updateData).length === 0) {
    throw new Error('No fields to update')
  }
  
  const { data, error } = await supabaseAdmin.from('appy_appraisal_periods')
    .update(updateData)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}