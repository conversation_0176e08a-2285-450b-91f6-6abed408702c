import { getCurrentUser } from '@/lib/auth'
import { auth, currentUser } from '@clerk/nextjs/server'

export default async function DebugAuthPage() {
  const clerkAuth = await auth()
  const clerkUser = await currentUser()
  const appUser = await getCurrentUser()

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Authentication Debug</h1>
      
      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Clerk Auth</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(clerkAuth, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Clerk User</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify({
              id: clerkUser?.id,
              email: clerkUser?.emailAddresses[0]?.emailAddress,
              firstName: clerkUser?.firstName,
              lastName: clerkUser?.lastName,
              fullName: `${clerkUser?.firstName} ${clerkUser?.lastName}`.trim(),
              publicMetadata: clerkUser?.publicMetadata,
              privateMetadata: clerkUser?.privateMetadata
            }, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">App User (from getCurrentUser)</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(appUser, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  )
}
