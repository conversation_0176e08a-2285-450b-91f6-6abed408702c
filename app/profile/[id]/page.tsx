import { notFound } from "next/navigation"
import { PublicEmployeeProfile } from "@/components/public-employee-profile"
import { getEmployeeProfile } from "@/lib/data/employees"

export default async function PublicProfilePage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  const { id } = await params
  const employee = await getEmployeeProfile(id)
  
  if (!employee || !employee.active) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-background">
      <PublicEmployeeProfile employee={employee} />
    </div>
  )
}