import { supabaseAdmin } from '../../supabase'

// Appraisal Template Functions
export async function getTemplates(filters?: {
  departmentId?: string
  isActive?: boolean
  createdBy?: string
}): Promise<any[]> {
  let query = supabaseAdmin
    .from('appy_appraisal_templates')
    .select(`
      *,
      appy_departments (
        name
      )
    `)
    .order('is_default', { ascending: false })
    .order('name', { ascending: true })
  
  if (filters?.departmentId) {
    query = query.eq('department_id', filters.departmentId)
  }
  
  if (filters?.isActive !== undefined) {
    query = query.eq('is_active', filters.isActive)
  }
  
  if (filters?.createdBy) {
    query = query.eq('created_by', filters.createdBy)
  }
  
  const { data, error } = await query
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data || []
}

export async function getTemplateById(id: string): Promise<any | null> {
  const { data, error } = await supabaseAdmin
    .from('appy_appraisal_templates')
    .select(`
      *,
      appy_departments (
        name
      )
    `)
    .eq('id', id)
    .single()
  
  if (error) {
    if (error.code === 'PGRST116') return null // Not found
    throw new Error(error.message)
  }
  
  return data
}

export async function getDefaultTemplate(departmentId?: string): Promise<any | null> {
  let query = supabaseAdmin
    .from('appy_appraisal_templates')
    .select(`
      *,
      appy_departments (
        name
      )
    `)
    .eq('is_active', true)
    .eq('is_default', true)
  
  if (departmentId) {
    query = query.or(`department_id.eq.${departmentId},department_id.is.null`)
  }
  
  const { data, error } = await query
    .order('department_id', { ascending: false, nullsFirst: false })
    .limit(1)
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data?.[0] || null
}

export async function createTemplate(templateData: {
  name: string
  description?: string
  questions: any
  departmentId?: string
  roleFilter?: string
  isActive?: boolean
  isDefault?: boolean
  createdBy: string
}): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_appraisal_templates')
    .insert({
      name: templateData.name,
      description: templateData.description,
      questions: templateData.questions,
      department_id: templateData.departmentId,
      role_filter: templateData.roleFilter,
      is_active: templateData.isActive ?? true,
      is_default: templateData.isDefault ?? false,
      created_by: templateData.createdBy
    })
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function updateTemplate(id: string, templateData: {
  name?: string
  description?: string
  questions?: any
  departmentId?: string
  roleFilter?: string
  isActive?: boolean
  isDefault?: boolean
}): Promise<any> {
  const updateData: any = {}
  
  if (templateData.name !== undefined) updateData.name = templateData.name
  if (templateData.description !== undefined) updateData.description = templateData.description
  if (templateData.questions !== undefined) updateData.questions = templateData.questions
  if (templateData.departmentId !== undefined) updateData.department_id = templateData.departmentId
  if (templateData.roleFilter !== undefined) updateData.role_filter = templateData.roleFilter
  if (templateData.isActive !== undefined) updateData.is_active = templateData.isActive
  if (templateData.isDefault !== undefined) updateData.is_default = templateData.isDefault
  
  const { data, error } = await supabaseAdmin
    .from('appy_appraisal_templates')
    .update(updateData)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function deleteTemplate(id: string): Promise<void> {
  const { error } = await supabaseAdmin
    .from('appy_appraisal_templates')
    .delete()
    .eq('id', id)
  
  if (error) {
    throw new Error(error.message)
  }
}

export async function duplicateTemplate(id: string, newName: string, createdBy: string): Promise<any> {
  // Get the original template
  const original = await getTemplateById(id)
  if (!original) {
    throw new Error('Template not found')
  }
  
  // Create a copy
  const { data, error } = await supabaseAdmin
    .from('appy_appraisal_templates')
    .insert({
      name: newName,
      description: original.description,
      questions: original.questions,
      department_id: original.department_id,
      role_filter: original.role_filter,
      is_active: true,
      is_default: false,
      created_by: createdBy
    })
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function trackTemplateUsage(templateId: string, periodId: string, managerId: string): Promise<void> {
  const { error } = await supabaseAdmin
    .from('appy_template_usage')
    .upsert({
      template_id: templateId,
      period_id: periodId,
      manager_id: managerId,
      usage_count: 1
    }, {
      onConflict: 'template_id,period_id,manager_id'
    })
  
  if (error) {
    throw new Error(error.message)
  }
}

export async function getTemplateUsageStats(templateId?: string): Promise<any[]> {
  let query = supabaseAdmin
    .from('appy_template_usage')
    .select(`
      *,
      appy_appraisal_templates (
        name
      ),
      appy_appraisal_periods (
        name,
        start_date,
        end_date
      )
    `)
    .order('created_at', { ascending: false })
  
  if (templateId) {
    query = query.eq('template_id', templateId)
  }
  
  const { data, error } = await query
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data || []
}