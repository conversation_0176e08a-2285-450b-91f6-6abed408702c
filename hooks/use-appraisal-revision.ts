"use client"

import { useState } from "react"
import { createAppraisalRevisionAction, createRevisionFromSubmittedAction } from "@/lib/actions"

export function useAppraisalRevision() {
  const [isCreatingRevision, setIsCreatingRevision] = useState(false)
  const [revisionError, setRevisionError] = useState<string | null>(null)

  const handleCreateRevision = async (appraisalId: string) => {
    setRevisionError(null)
    setIsCreatingRevision(true)

    try {
      const result = await createAppraisalRevisionAction(appraisalId)

      if (result.success) {
        if (typeof window !== 'undefined') {
          window.location.reload()
        }
      } else {
        setRevisionError('error' in result ? result.error : 'Failed to create revision')
      }
    } catch (error) {
      console.error('Revision creation error:', error)
      setRevisionError('An unexpected error occurred. Please try again.')
    } finally {
      setIsCreatingRevision(false)
    }
  }

  const handleCreateRevisionFromSubmitted = async (appraisalId: string) => {
    setRevisionError(null)
    setIsCreatingRevision(true)

    try {
      const result = await createRevisionFromSubmittedAction(appraisalId)

      if (result.success) {
        if (typeof window !== 'undefined') {
          window.location.reload()
        }
      } else {
        setRevisionError('error' in result ? result.error : 'Failed to create new revision')
      }
    } catch (error) {
      console.error('Revision creation from submitted error:', error)
      setRevisionError('An unexpected error occurred. Please try again.')
    } finally {
      setIsCreatingRevision(false)
    }
  }

  return {
    isCreatingRevision,
    revisionError,
    handleCreateRevision,
    handleCreateRevisionFromSubmitted,
    setRevisionError,
  }
}