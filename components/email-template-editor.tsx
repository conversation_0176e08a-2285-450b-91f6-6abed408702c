'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { FileText, Save, Loader2, Eye, Code, Hash } from 'lucide-react'

interface Props {
  templates: any[]
  settings: Record<string, any>
  onSave: (templates: any[]) => void
  saving: boolean
}

export function EmailTemplateEditor({ templates, settings, onSave, saving }: Props) {
  const [selectedTemplate, setSelectedTemplate] = useState(templates[0]?.template_key || '')
  const [templateData, setTemplateData] = useState(() => {
    const template = templates.find(t => t.template_key === selectedTemplate)
    return template || {}
  })

  const handleTemplateSelect = (templateKey: string) => {
    setSelectedTemplate(templateKey)
    const template = templates.find(t => t.template_key === templateKey)
    setTemplateData(template || {})
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const updatedTemplates = templates.map(template => {
      if (template.template_key === selectedTemplate) {
        return { ...template, ...templateData }
      }
      return template
    })
    
    onSave(updatedTemplates)
  }

  const handleInputChange = (field: string, value: any) => {
    setTemplateData((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  // Sample data for preview
  const sampleData = {
    manager_name: 'John Manager',
    count: 2,
    count_plural: 's',
    period_end_formatted: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(),
    employee_list: '<li><strong>Alice Johnson</strong> (Engineering)</li><li><strong>Bob Smith</strong> (Marketing)</li>',
    employee_list_text: '• Alice Johnson (Engineering)\n• Bob Smith (Marketing)',
    dashboard_url: 'https://company.com/dashboard',
    email_signature: settings.email_signature?.value || 'Best regards,<br>The HR Team'
  }

  // Simple template variable substitution for preview
  const renderPreview = (template: string) => {
    let rendered = template
    Object.entries(sampleData).forEach(([key, value]) => {
      const regex = new RegExp('\\{\\{' + key + '\\}\\}', 'g')
      rendered = rendered.replace(regex, String(value))
    })
    return rendered
  }

  if (!templates.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Email Templates</CardTitle>
          <CardDescription>No email templates found</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Email templates need to be configured in the database.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Email Template Editor
        </CardTitle>
        <CardDescription>
          Customize email templates with variable substitution
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Template Selection */}
          <div className="space-y-2">
            <Label htmlFor="template-select">Select Template</Label>
            <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a template to edit" />
              </SelectTrigger>
              <SelectContent>
                {templates.map((template) => (
                  <SelectItem key={template.template_key} value={template.template_key}>
                    {template.template_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedTemplate && templateData && (
            <>
              {/* Template Name */}
              <div className="space-y-2">
                <Label htmlFor="template_name">Template Name</Label>
                <Input
                  id="template_name"
                  value={templateData.template_name || ''}
                  onChange={(e) => handleInputChange('template_name', e.target.value)}
                  placeholder="Template name"
                  required
                />
              </div>

              {/* Subject Line */}
              <div className="space-y-2">
                <Label htmlFor="subject_template">Subject Line</Label>
                <Input
                  id="subject_template"
                  value={templateData.subject_template || ''}
                  onChange={(e) => handleInputChange('subject_template', e.target.value)}
                  placeholder="Email subject with {{variables}}"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Preview: {renderPreview(templateData.subject_template || '')}
                </p>
              </div>

              {/* Available Variables */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Available Variables
                </Label>
                <div className="flex flex-wrap gap-2">
                  {(templateData.available_variables || []).map((variable: string) => (
                    <Badge key={variable} variant="secondary" className="text-xs">
                      {`{{${variable}}}`}
                    </Badge>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Template Content */}
              <Tabs defaultValue="html" className="space-y-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="html">
                    <Code className="h-4 w-4 mr-2" />
                    HTML Template
                  </TabsTrigger>
                  <TabsTrigger value="text">
                    <FileText className="h-4 w-4 mr-2" />
                    Text Template
                  </TabsTrigger>
                  <TabsTrigger value="preview">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="html" className="space-y-2">
                  <Label htmlFor="html_template">HTML Template</Label>
                  <Textarea
                    id="html_template"
                    value={templateData.html_template || ''}
                    onChange={(e) => handleInputChange('html_template', e.target.value)}
                    placeholder="HTML email template with {{variables}}"
                    rows={12}
                    className="font-mono text-sm"
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Use HTML tags for formatting. Variables like {`{{manager_name}}`} will be replaced automatically.
                  </p>
                </TabsContent>

                <TabsContent value="text" className="space-y-2">
                  <Label htmlFor="text_template">Plain Text Template</Label>
                  <Textarea
                    id="text_template"
                    value={templateData.text_template || ''}
                    onChange={(e) => handleInputChange('text_template', e.target.value)}
                    placeholder="Plain text email template with {{variables}}"
                    rows={12}
                    className="font-mono text-sm"
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Plain text version for email clients that don't support HTML.
                  </p>
                </TabsContent>

                <TabsContent value="preview" className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium">Subject Preview</Label>
                      <div className="p-3 bg-muted rounded-md text-sm">
                        {renderPreview(templateData.subject_template || '')}
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium">HTML Preview</Label>
                      <div 
                        className="p-4 bg-white border rounded-md text-sm"
                        style={{ maxHeight: '400px', overflow: 'auto' }}
                        dangerouslySetInnerHTML={{ 
                          __html: renderPreview(templateData.html_template || '') 
                        }}
                      />
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <Separator />

              {/* Save Button */}
              <div className="flex justify-end">
                <Button type="submit" disabled={saving}>
                  {saving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Template
                    </>
                  )}
                </Button>
              </div>
            </>
          )}
        </form>
      </CardContent>
    </Card>
  )
}