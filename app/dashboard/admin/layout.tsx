import { requireRole } from '@/lib/auth'
import { redirect } from 'next/navigation'

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Require super-admin or admin role
  const user = await requireRole(['super-admin', 'admin'])
  
  // Super-admin has access to all admin pages
  // Regular admins will be restricted at the page level
  
  return (
    <div className="admin-layout">
      {children}
    </div>
  )
}