import { requireRole, getCurrentUser } from '@/lib/auth'
import { notFound, redirect } from 'next/navigation'
import { AdminDashboard } from '@/components/admin-dashboard'
import { getAdminProfile, getAdminPermissions, getAdminStats, getAllAdmins } from '@/lib/admin-data'

interface Props {
  params: Promise<{ adminId: string }>
}

export default async function AdminPage({ params }: Props) {
  const currentUser = await requireRole(['super-admin', 'admin'])
  const { adminId } = await params
  
  // Get admin profile information
  const adminProfile = await getAdminProfile(adminId)
  
  if (!adminProfile) {
    notFound()
  }
  
  // Access control: users can only access their own admin page
  // unless they are super-admin
  if (currentUser.role !== 'super-admin' && currentUser.id !== adminId) {
    redirect('/dashboard?error=access_denied')
  }
  
  // Get permissions for this admin
  const permissions = await getAdminPermissions(adminId)
  
  // Get admin stats
  const stats = await getAdminStats(adminId, currentUser.id, currentUser.role)
  
  // Get all admins if super-admin
  const allAdmins = currentUser.role === 'super-admin' 
    ? await getAllAdmins(currentUser.id, currentUser.role)
    : []
  
  return (
    <AdminDashboard
      admin={adminProfile}
      permissions={permissions}
      currentUser={currentUser}
      stats={stats}
      allAdmins={allAdmins}
    />
  )
}

export async function generateMetadata({ params }: Props) {
  const { adminId } = await params
  const adminProfile = await getAdminProfile(adminId)
  
  if (!adminProfile) {
    return {
      title: 'Admin Not Found',
    }
  }
  
  return {
    title: `${adminProfile.fullName} - Admin Dashboard`,
    description: `Admin dashboard for ${adminProfile.fullName}`,
  }
}