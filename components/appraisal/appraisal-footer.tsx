"use client"

import { <PERSON><PERSON>oot<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Send, Loader, CheckCircle, Edit3, RotateCcw } from "lucide-react"
import { AutosaveStatusIndicator } from "@/components/appraisal/autosave-status-indicator"
import { useAutosave } from "@/hooks/use-autosave"
import type { AppraisalDetails } from "@/lib/types"

type AppraisalFooterProps = {
  appraisal: AppraisalDetails | null
  isSubmitted: boolean
  submitSuccess: boolean
  isPending: boolean
  isCreatingRevision: boolean
  autosave: ReturnType<typeof useAutosave>
  onSubmit: () => void
  onCreateRevision: () => void
  onCreateRevisionFromSubmitted: () => void
}

export function AppraisalFooter({
  appraisal,
  isSubmitted,
  submitSuccess,
  isPending,
  isCreatingRevision,
  autosave,
  onSubmit,
  onCreateRevision,
  onCreateRevisionFromSubmitted,
}: AppraisalFooterProps) {
  return (
    <CardFooter className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        {!isSubmitted && !submitSuccess && (
          <AutosaveStatusIndicator autosave={autosave} />
        )}
      </div>

      <div className="flex items-center gap-4">
        {isSubmitted || submitSuccess ? (
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span>
                Appraisal {appraisal?.isRevision ? 'revision ' : ''}submitted successfully
                {appraisal?.revisionNumber && appraisal.revisionNumber > 1 && (
                  <span className="ml-1">(v{appraisal.revisionNumber})</span>
                )}
              </span>
            </div>
            {!appraisal?.isRevision ? (
              <Button
                onClick={onCreateRevision}
                disabled={isCreatingRevision}
                variant="outline"
                size="sm"
                className="min-w-[140px]"
              >
                {isCreatingRevision ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" /> Creating...
                  </>
                ) : (
                  <>
                    <Edit3 className="mr-2 h-4 w-4" /> Edit Appraisal
                  </>
                )}
              </Button>
            ) : (
              <Button
                onClick={onCreateRevisionFromSubmitted}
                disabled={isCreatingRevision}
                variant="outline"
                size="sm"
                className="min-w-[140px]"
              >
                {isCreatingRevision ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" /> Creating...
                  </>
                ) : (
                  <>
                    <RotateCcw className="mr-2 h-4 w-4" /> Edit Revision
                  </>
                )}
              </Button>
            )}
          </div>
        ) : (
          <>
            <p className="text-xs text-muted-foreground">
              <span className="text-red-500">*</span> Required fields
            </p>
            <Button
              onClick={onSubmit}
              disabled={isPending}
              className="min-w-[140px]"
            >
              {isPending ? (
                <>
                  <Loader className="mr-2 h-4 w-4 animate-spin" /> 
                  {appraisal?.isRevision ? 'Resubmitting...' : 'Submitting...'}
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" /> 
                  {appraisal?.isRevision ? 'Resubmit Revision' : 'Submit Appraisal'}
                </>
              )}
            </Button>
          </>
        )}
      </div>
    </CardFooter>
  )
}