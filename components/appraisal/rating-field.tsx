"use client"

import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { performanceRatings } from "@/lib/constants/appraisal-ratings"

type RatingFieldProps = {
  question: string
  value: number | null
  onRatingChange: (value: number) => void
  comment?: string
  onCommentChange?: (value: string) => void
  commentLabel?: string
  commentPlaceholder?: string
  disabled?: boolean
  required?: boolean
  error?: string
  commentRequired?: boolean
  commentError?: string
}

export function RatingField({
  question,
  value,
  onRatingChange,
  comment,
  onCommentChange,
  commentLabel = "Comment (optional):",
  commentPlaceholder = "Optional comment...",
  disabled = false,
  required = false,
  error,
  commentRequired = false,
  commentError,
}: RatingFieldProps) {
  return (
    <div className="space-y-4">
      <div>
        <Label className="text-sm font-medium">
          {question} {required && <span className="text-red-500">*</span>}
        </Label>
        <p className="text-sm text-muted-foreground">Rating (1–5):</p>
      </div>
      <RadioGroup
        value={value?.toString() ?? ""}
        onValueChange={(val) => onRatingChange(parseInt(val))}
        className="flex flex-row gap-4"
        disabled={disabled}
      >
        {performanceRatings.map((rating) => (
          <div key={rating} className="flex items-center space-x-2">
            <RadioGroupItem value={rating.toString()} id={`rating-${rating}`} />
            <Label htmlFor={`rating-${rating}`} className="text-sm">{rating}</Label>
          </div>
        ))}
      </RadioGroup>
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}

      {onCommentChange && (
        <div className="space-y-2">
          <Label htmlFor="comment">
            {commentLabel} {commentRequired && <span className="text-red-500">*</span>}
          </Label>
          <Textarea
            id="comment"
            value={comment || ""}
            onChange={(e) => onCommentChange(e.target.value)}
            placeholder={commentPlaceholder}
            rows={commentRequired ? 3 : 2}
            disabled={disabled}
          />
          {commentError && (
            <p className="text-sm text-red-500">{commentError}</p>
          )}
        </div>
      )}
    </div>
  )
}