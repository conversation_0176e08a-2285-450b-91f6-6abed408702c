/**
 * Debug utility for conditional logging
 * Set DEBUG_MODE to true to enable debug logs, false to disable them
 */

// You can control debug mode via environment variable or toggle this manually
const DEBUG_MODE = process.env.NODE_ENV === 'development' && process.env.ENABLE_DEBUG_LOGS !== 'false'

/**
 * Debug logger that only logs when DEBUG_MODE is enabled
 */
export const debug = {
  log: (...args: any[]) => {
    if (DEBUG_MODE) {
      console.log(...args)
    }
  },
  
  error: (...args: any[]) => {
    if (DEBUG_MODE) {
      console.error(...args)
    }
  },
  
  warn: (...args: any[]) => {
    if (DEBUG_MODE) {
      console.warn(...args)
    }
  },
  
  info: (...args: any[]) => {
    if (DEBUG_MODE) {
      console.info(...args)
    }
  },
  
  // Check if debug mode is enabled
  isEnabled: () => DEBUG_MODE,
  
  // Conditional execution - only runs the function if debug mode is enabled
  run: (fn: () => void) => {
    if (DEBUG_MODE) {
      fn()
    }
  }
}

/**
 * Environment-aware console that respects debug settings
 * Use this instead of console.log for debug messages
 */
export const debugConsole = debug

// Export a function to check debug status
export const isDebugEnabled = () => DEBUG_MODE
