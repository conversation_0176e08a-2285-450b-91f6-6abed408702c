"use client"

import { RatingField } from "@/components/appraisal/rating-field"
import { appraisalQuestions } from "@/lib/constants/appraisal-questions"
import type { AppraisalFormData, AppraisalInputChangeHandler, FormError } from "@/components/appraisal/types"

type PerformanceRatingsSectionProps = {
  formData: AppraisalFormData
  onInputChange: AppraisalInputChangeHandler
  disabled: boolean
  errors: {
    impactRating?: FormError
    qualityRating?: FormError
    collaborationRating?: FormError
    skillGrowthRating?: FormError
  }
}

export function PerformanceRatingsSection({
  formData,
  onInputChange,
  disabled,
  errors,
}: PerformanceRatingsSectionProps) {
  return (
    <div className="space-y-8 border-t pt-8">
      <h3 className="text-lg font-semibold">Performance Ratings</h3>

      <RatingField
        question={appraisalQuestions.impactRating}
        value={formData.impactRating ?? null}
        onRatingChange={(value) => onInputChange("impactRating", value)}
        comment={formData.impactComment}
        onCommentChange={(value) => onInputChange("impactComment", value)}
        commentPlaceholder="Optional comment for impact rating..."
        disabled={disabled}
        required
        error={errors.impactRating?.message}
      />

      <RatingField
        question={appraisalQuestions.qualityRating}
        value={formData.qualityRating ?? null}
        onRatingChange={(value) => onInputChange("qualityRating", value)}
        comment={formData.qualityComment}
        onCommentChange={(value) => onInputChange("qualityComment", value)}
        commentPlaceholder="Optional comment for quality rating..."
        disabled={disabled}
        required
        error={errors.qualityRating?.message}
      />

      <RatingField
        question={appraisalQuestions.collaborationRating}
        value={formData.collaborationRating ?? null}
        onRatingChange={(value) => onInputChange("collaborationRating", value)}
        comment={formData.collaborationComment}
        onCommentChange={(value) => onInputChange("collaborationComment", value)}
        commentPlaceholder="Optional comment for collaboration rating..."
        disabled={disabled}
        required
        error={errors.collaborationRating?.message}
      />

      <RatingField
        question={appraisalQuestions.skillGrowthRating}
        value={formData.skillGrowthRating ?? null}
        onRatingChange={(value) => onInputChange("skillGrowthRating", value)}
        comment={formData.skillGrowthComment}
        onCommentChange={(value) => onInputChange("skillGrowthComment", value)}
        commentPlaceholder="Optional comment for skill growth rating..."
        disabled={disabled}
        required
        error={errors.skillGrowthRating?.message}
      />
    </div>
  )
}