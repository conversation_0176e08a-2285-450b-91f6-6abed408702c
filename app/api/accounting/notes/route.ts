import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { addAccountingNotes, addAccountingComment } from '@/lib/data/accounting'
import { z } from 'zod'

const addAccountingNotesSchema = z.object({
  appraisalId: z.string().uuid(),
  notes: z.string().min(10).max(1000),
  adjustmentAmount: z.number().optional().nullable(),
  adjustmentType: z.enum(['deduction', 'bonus', 'none']).optional().nullable(),
  adjustmentReason: z.string().optional().nullable(),
  commentType: z.enum(['general', 'deduction', 'bonus', 'correction', 'payment_issue']).default('general'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal')
})

/**
 * POST - Add accounting notes and salary adjustments to an appraisal
 */
export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has accounting permissions
    if (!['accountant', 'hr-admin', 'super-admin'].includes(currentUser.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = addAccountingNotesSchema.parse(body)

    // Add accounting notes to the appraisal
    const notesResult = await addAccountingNotes(
      validatedData.appraisalId,
      validatedData.notes,
      currentUser.id,
      validatedData.adjustmentAmount || undefined,
      validatedData.adjustmentType === 'none' ? undefined : validatedData.adjustmentType || undefined,
      validatedData.adjustmentReason || undefined
    )

    if (!notesResult.success) {
      return NextResponse.json({ 
        error: notesResult.error || 'Failed to add accounting notes' 
      }, { status: 500 })
    }

    // Also add as a comment for tracking
    const commentResult = await addAccountingComment(
      validatedData.appraisalId,
      currentUser.id,
      validatedData.notes,
      validatedData.commentType,
      validatedData.priority,
      true // Internal comment
    )

    if (!commentResult.success) {
      console.error('Failed to add accounting comment:', commentResult.error)
      // Don't fail the request if comment fails, notes were saved successfully
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Accounting notes saved successfully' 
    })

  } catch (error) {
    console.error('Error adding accounting notes:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid input data',
        details: error.errors 
      }, { status: 400 })
    }

    return NextResponse.json({ 
      error: 'Failed to add accounting notes' 
    }, { status: 500 })
  }
}
