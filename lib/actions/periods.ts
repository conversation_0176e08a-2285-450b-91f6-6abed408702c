"use server"

import { revalidatePath } from "next/cache"
import { savePeriod } from "../data/index"
import { appraisalPeriodFormSchema } from "../schemas"
import {
  requirePermission,
  checkRateLimit,
  logUserAction,
  validateSession
} from "../auth"
import {
  handleServerActionError,
  RateLimitError
} from "./shared"

export async function savePeriodAction(formData: FormData) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'period-save', 5, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('period:write')

    // Extract and validate data
    const rawData = {
      id: formData.get('id') as string || undefined,
      periodStart: formData.get('periodStart') as string,
      periodEnd: formData.get('periodEnd') as string,
      closed: formData.get('closed') === 'true',
    }

    const validatedData = appraisalPeriodFormSchema.parse(rawData)

    // Log the action
    await logUserAction('period:save', {
      periodStart: validatedData.periodStart,
      periodEnd: validatedData.periodEnd,
      isUpdate: !!rawData.id
    })

    // Save to database
    await savePeriod(validatedData)

    // Revalidate cache
    revalidatePath("/dashboard/periods")

    return { success: true, message: "Appraisal Period saved successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}