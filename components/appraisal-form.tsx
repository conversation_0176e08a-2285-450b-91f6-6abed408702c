"use client"

import React from "react"
import type { AppraisalDetails, EmployeeDetails } from "@/lib/types"
import { Card, CardContent } from "@/components/ui/card"
import { useAppraisalForm } from "@/hooks/use-appraisal-form"
import { useAppraisalRevision } from "@/hooks/use-appraisal-revision"
import { AppraisalAlerts } from "@/components/appraisal/appraisal-alerts"
import { AppraisalHeader } from "@/components/appraisal/appraisal-header"
import { AppraisalFooter } from "@/components/appraisal/appraisal-footer"
import { BasicReviewSection } from "@/components/appraisal/sections/basic-review-section"
import { PerformanceAssessmentSection } from "@/components/appraisal/sections/performance-assessment-section"
import { DisciplineRatingSection } from "@/components/appraisal/sections/discipline-rating-section"
import { PerformanceRatingsSection } from "@/components/appraisal/sections/performance-ratings-section"
import { ReadinessCompensationSection } from "@/components/appraisal/sections/readiness-compensation-section"
import { PaymentStatusSection } from "@/components/appraisal/sections/payment-status-section"

type AppraisalFormProps = {
  employee: EmployeeDetails
  appraisal: AppraisalDetails | null
}

export function AppraisalForm({ employee, appraisal }: AppraisalFormProps) {
  const {
    formData,
    submissionForm,
    autosave,
    isPending,
    submitError,
    submitSuccess,
    handleInputChange,
    handleSubmit,
  } = useAppraisalForm(employee, appraisal)

  const {
    isCreatingRevision,
    revisionError,
    handleCreateRevision,
    handleCreateRevisionFromSubmitted,
  } = useAppraisalRevision()

  const isSubmitted = appraisal?.status === "submitted"
  const disabled = isSubmitted || submitSuccess

  const formErrors = submissionForm.formState.errors

  return (
    <div className="grid gap-6">
      <AppraisalAlerts
        submitSuccess={submitSuccess}
        submitError={submitError}
        revisionError={revisionError}
      />

      <Card>
        <AppraisalHeader employee={employee} appraisal={appraisal} />
        
        <CardContent className="space-y-8">
          <BasicReviewSection
            formData={formData}
            onInputChange={handleInputChange}
            disabled={disabled}
            errors={formErrors}
          />

          <PerformanceAssessmentSection
            formData={formData}
            onInputChange={handleInputChange}
            disabled={disabled}
            errors={formErrors}
          />

          <DisciplineRatingSection
            formData={formData}
            onInputChange={handleInputChange}
            disabled={disabled}
            errors={formErrors}
          />

          <PerformanceRatingsSection
            formData={formData}
            onInputChange={handleInputChange}
            disabled={disabled}
            errors={formErrors}
          />

          <ReadinessCompensationSection
            formData={formData}
            onInputChange={handleInputChange}
            disabled={disabled}
            errors={formErrors}
          />

          <PaymentStatusSection
            formData={formData}
            onInputChange={handleInputChange}
            disabled={disabled}
            errors={formErrors}
          />
        </CardContent>

        <AppraisalFooter
          appraisal={appraisal}
          isSubmitted={isSubmitted}
          submitSuccess={submitSuccess}
          isPending={isPending}
          isCreatingRevision={isCreatingRevision}
          autosave={autosave}
          onSubmit={handleSubmit}
          onCreateRevision={() => appraisal?.id && handleCreateRevision(appraisal.id)}
          onCreateRevisionFromSubmitted={() => appraisal?.id && handleCreateRevisionFromSubmitted(appraisal.id)}
        />
      </Card>
    </div>
  )
}