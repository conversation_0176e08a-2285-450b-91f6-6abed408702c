"use client"

import { Employ<PERSON><PERSON><PERSON> } from "@/lib/types"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Edit, Trash, Plus } from "lucide-react"
import { useState } from "react"
import { EmployeeKPIForm } from "./employee-kpi-form"
import { deleteEmployeeKPI } from "@/lib/actions/employee-kpis"
import { toast } from "sonner"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface EmployeeKPIListProps {
  employeeId: string
  kpis: EmployeeKPI[]
  canManage?: boolean
}

export function EmployeeKPIList({ employeeId, kpis, canManage = false }: EmployeeKPIListProps) {
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingKPI, setEditingKPI] = useState<EmployeeKPI | null>(null)
  const [deletingKPI, setDeletingKPI] = useState<EmployeeKPI | null>(null)

  const handleDelete = async () => {
    if (!deletingKPI) return

    try {
      const result = await deleteEmployeeKPI(deletingKPI.id)
      if (result.success) {
        toast.success("KPI deleted successfully")
      } else {
        toast.error("error" in result ? result.error : "Failed to delete KPI")
      }
    } catch (error) {
      console.error("Error deleting KPI:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setDeletingKPI(null)
    }
  }

  if (showAddForm || editingKPI) {
    return (
      <EmployeeKPIForm
        employeeId={employeeId}
        kpi={editingKPI}
        onClose={() => {
          setShowAddForm(false)
          setEditingKPI(null)
        }}
      />
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Key Performance Indicators</CardTitle>
              <CardDescription>Track and manage employee KPIs</CardDescription>
            </div>
            {canManage && (
              <Button size="sm" onClick={() => setShowAddForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add KPI
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {kpis.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-8">
              No KPIs have been set up yet.
            </p>
          ) : (
            <div className="space-y-4">
              {kpis.map((kpi) => (
                <div key={kpi.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1 flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="text-sm font-medium">{kpi.kpiName}</h4>
                        {kpi.period && (
                          <Badge variant="outline" className="text-xs">
                            {kpi.period}
                          </Badge>
                        )}
                      </div>
                      {kpi.description && (
                        <p className="text-xs text-muted-foreground">{kpi.description}</p>
                      )}
                      <div className="flex items-center gap-4 text-sm mt-2">
                        {kpi.kpiValue && (
                          <div>
                            <span className="text-muted-foreground">Current: </span>
                            <span className="font-medium">
                              {kpi.kpiValue}
                              {kpi.kpiUnit && ` ${kpi.kpiUnit}`}
                            </span>
                          </div>
                        )}
                        {kpi.kpiTarget && (
                          <div>
                            <span className="text-muted-foreground">Target: </span>
                            <span className="font-medium">
                              {kpi.kpiTarget}
                              {kpi.kpiUnit && ` ${kpi.kpiUnit}`}
                            </span>
                          </div>
                        )}
                      </div>
                      {(kpi.kpiValue && kpi.kpiTarget && !isNaN(Number(kpi.kpiValue)) && !isNaN(Number(kpi.kpiTarget))) && (
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div 
                            className="bg-primary h-2 rounded-full transition-all"
                            style={{ 
                              width: `${Math.min(100, (Number(kpi.kpiValue) / Number(kpi.kpiTarget)) * 100)}%` 
                            }}
                          />
                        </div>
                      )}
                    </div>
                    {canManage && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => setEditingKPI(kpi)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => setDeletingKPI(kpi)}
                            className="text-destructive"
                          >
                            <Trash className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <AlertDialog open={!!deletingKPI} onOpenChange={() => setDeletingKPI(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the KPI "{deletingKPI?.kpiName}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}