import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Fetching feedback statistics...')
    
    const { userId } = await auth()
    if (!userId) {
      console.log('❌ Unauthorized feedback stats request')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has HR or admin permissions
    const userRole = await db.getUserRole(userId)
    if (!userRole || !['hr-admin', 'super-admin'].includes(userRole)) {
      console.log('❌ Insufficient permissions for feedback stats:', userRole)
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    console.log('📊 Fetching feedback statistics with date range:', {
      startDate,
      endDate
    })

    // Fetch statistics
    const stats = await db.getFeedbackStatistics(startDate || undefined, endDate || undefined)
    
    // Fetch pending feedback for HR
    const pendingFeedback = await db.getPendingFeedbackForHR()

    console.log('✅ Feedback statistics fetched successfully')

    return NextResponse.json({
      success: true,
      statistics: stats,
      pendingFeedback,
      pendingCount: pendingFeedback.length
    })

  } catch (error) {
    console.error('❌ Error fetching feedback statistics:', error)
    return NextResponse.json({
      error: 'Failed to fetch feedback statistics'
    }, { status: 500 })
  }
}
