// Re-export all actions from the modular actions directory
// This file maintains backward compatibility while keeping actions organized by domain

// Import and re-export all actions from the organized modules
// Note: Each individual action module contains "use server" directive
export * from './actions/index'

// Note: All server actions have been split into domain-specific modules:
// - lib/actions/shared.ts - Error handling and common utilities
// - lib/actions/departments.ts - Department CRUD operations
// - lib/actions/employees.ts - Employee CRUD operations
// - lib/actions/periods.ts - Appraisal period operations
// - lib/actions/appraisals.ts - Appraisal drafts, submissions, and revisions
// - lib/actions/search.ts - Global search functionality
// - lib/actions/approvals.ts - Approval/rejection operations
// - lib/actions/bulk.ts - Bulk operations
// - lib/actions/export.ts - Data export operations
// - lib/actions/pto.ts - PTO request operations
//
// This modular structure improves:
// - Code organization and maintainability
// - Easier testing and debugging
// - Better separation of concerns
// - Reduced file size and complexity
// - Improved code reusability