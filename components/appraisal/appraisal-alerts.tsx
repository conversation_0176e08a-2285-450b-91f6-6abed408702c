"use client"

import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, AlertTriangle } from "lucide-react"

type AppraisalAlertsProps = {
  submitSuccess: boolean
  submitError: string | null
  revisionError: string | null
}

export function AppraisalAlerts({ 
  submitSuccess, 
  submitError, 
  revisionError 
}: AppraisalAlertsProps) {
  return (
    <>
      {submitSuccess && (
        <Alert
          className="border-green-200 bg-green-50"
          role="alert"
          aria-live="polite"
        >
          <CheckCircle className="h-4 w-4 text-green-600" aria-hidden="true" />
          <AlertDescription className="text-green-800">
            Appraisal submitted successfully! The form is now read-only.
          </AlertDescription>
        </Alert>
      )}

      {submitError && (
        <Alert
          variant="destructive"
          role="alert"
          aria-live="assertive"
        >
          <AlertTriangle className="h-4 w-4" aria-hidden="true" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {revisionError && (
        <Alert
          variant="destructive"
          role="alert"
          aria-live="assertive"
        >
          <AlertTriangle className="h-4 w-4" aria-hidden="true" />
          <AlertDescription>{revisionError}</AlertDescription>
        </Alert>
      )}
    </>
  )
}