import type { Metadata } from 'next'
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs'
import { ErrorBoundary } from '@/components/error-boundary'
import { Toaster } from '@/components/ui/sonner'
import './globals.css'

export const metadata: Metadata = {
  title: 'Employee Appraisal System',
  description: 'Secure employee performance appraisal management system',
  keywords: ['appraisal', 'performance', 'employee', 'management'],
  authors: [{ name: 'Your Company' }],
  robots: 'noindex, nofollow', // Prevent indexing of internal tool
  icons: {
    icon: [
      { url: '/placeholder-logo.svg', sizes: 'any', type: 'image/svg+xml' },
      { url: '/placeholder-logo.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/placeholder-logo.png', sizes: '180x180', type: 'image/png' },
    ],
    shortcut: '/placeholder-logo.svg',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body>
          <ErrorBoundary>
            {children}
            <Toaster />
          </ErrorBoundary>
        </body>
      </html>
    </ClerkProvider>
  )
}
