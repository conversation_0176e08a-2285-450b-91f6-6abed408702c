import { supabaseAdmin } from '../../supabase'
import type { Appraisal, AppraisalWithDetails } from '../core/types'

// Appraisals
export async function getAppraisalByEmployeeId(employeeId: string, periodId?: string): Promise<Appraisal | null> {
  let query = supabaseAdmin.from('appy_appraisals')
    .select('*')
    .eq('employee_id', employeeId)
    .order('created_at', { ascending: false })
  
  if (periodId) {
    query = query.eq('period_id', periodId)
  }
  
  const { data, error } = await query.limit(1).single()
  
  if (error) {
    if (error.code === 'PGRST116') {
      return null // Not found
    }
    throw new Error(error.message)
  }
  
  return data
}

export async function saveAppraisalDraft(appraisalData: {
  periodId: string
  employeeId: string
  managerId: string
  question1?: string | null
  question2?: string | null
  question3?: string | null
  question4?: string | null
  question5?: string | null
  paymentStatus?: string | null
  // New fields
  keyContributions?: string | null
  extraInitiatives?: string | null
  performanceLacking?: string | null
  disciplineRating?: number | null
  disciplineComment?: string | null
  daysOffTaken?: number | null
  impactRating?: number | null
  impactComment?: string | null
  qualityRating?: number | null
  qualityComment?: string | null
  collaborationRating?: number | null
  collaborationComment?: string | null
  skillGrowthRating?: number | null
  skillGrowthComment?: string | null
  readinessPromotion?: string | null
  readinessComment?: string | null
  compensationRecommendation?: string | null
}): Promise<Appraisal> {
  const { data, error } = await supabaseAdmin.from('appy_appraisals')
    .upsert({
      period_id: appraisalData.periodId,
      employee_id: appraisalData.employeeId,
      manager_id: appraisalData.managerId,
      question_1: appraisalData.question1 || null,
      question_2: appraisalData.question2 || null,
      question_3: appraisalData.question3 || null,
      question_4: appraisalData.question4 || null,
      question_5: appraisalData.question5 || null,
      payment_status: appraisalData.paymentStatus || null,
      // New fields
      key_contributions: appraisalData.keyContributions || null,
      extra_initiatives: appraisalData.extraInitiatives || null,
      performance_lacking: appraisalData.performanceLacking || null,
      discipline_rating: appraisalData.disciplineRating || null,
      discipline_comment: appraisalData.disciplineComment || null,
      days_off_taken: appraisalData.daysOffTaken || null,
      impact_rating: appraisalData.impactRating || null,
      impact_comment: appraisalData.impactComment || null,
      quality_rating: appraisalData.qualityRating || null,
      quality_comment: appraisalData.qualityComment || null,
      collaboration_rating: appraisalData.collaborationRating || null,
      collaboration_comment: appraisalData.collaborationComment || null,
      skill_growth_rating: appraisalData.skillGrowthRating || null,
      skill_growth_comment: appraisalData.skillGrowthComment || null,
      readiness_promotion: appraisalData.readinessPromotion || null,
      readiness_comment: appraisalData.readinessComment || null,
      compensation_recommendation: appraisalData.compensationRecommendation || null,
      status: 'pending'
    }, {
      onConflict: 'period_id,employee_id'
    })
    .select()
    .single()

  if (error) {
    throw new Error(error.message)
  }

  return data
}

export async function submitAppraisal(id: string, paymentStatus?: string): Promise<Appraisal> {
  const updateData: any = {
    status: 'submitted',
    submitted_at: new Date().toISOString(),
    last_edited_at: new Date().toISOString()
  }

  if (paymentStatus) {
    updateData.payment_status = paymentStatus
  }

  const { data, error } = await supabaseAdmin.from('appy_appraisals')
    .update(updateData)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    throw new Error(error.message)
  }

  return data
}

// Performance Statistics
export async function getPerformanceStatsByPeriod(periodId: string): Promise<Appraisal[]> {
  const { data, error } = await supabaseAdmin.from('appy_appraisals')
    .select('*')
    .eq('period_id', periodId)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data || []
}

export async function getAllAppraisalsForManager(managerId: string, periodId?: string): Promise<Appraisal[]> {
  let query = supabaseAdmin.from('appy_appraisals')
    .select('*')
    .eq('manager_id', managerId)
    .order('created_at', { ascending: false })
  
  if (periodId) {
    query = query.eq('period_id', periodId)
  }
  
  const { data, error } = await query
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data || []
}

export async function getAppraisalsWithEmployeeData(periodId?: string): Promise<AppraisalWithDetails[]> {
  let query = supabaseAdmin.from('appy_appraisals')
    .select(`
      *,
      appy_employees!inner (
        id,
        full_name,
        compensation,
        rate,
        appy_departments!inner (
          name
        )
      ),
      appy_managers!inner (
        full_name
      )
    `)
    .order('created_at', { ascending: false })
  
  if (periodId) {
    query = query.eq('period_id', periodId)
  }
  
  const { data, error } = await query
  
  if (error) {
    throw new Error(error.message)
  }
  
  return (data || []).map(appraisal => ({
    ...appraisal,
    employee_name: appraisal.appy_employees?.full_name || '',
    manager_name: appraisal.appy_managers?.full_name || '',
    department_name: appraisal.appy_employees?.appy_departments?.name || ''
  }))
}

// Get all submitted appraisals for a period
export async function getSubmittedAppraisals(periodId: string): Promise<Appraisal[]> {
  const { data, error } = await supabaseAdmin.from('appy_appraisals')
    .select('*')
    .eq('period_id', periodId)
    .eq('status', 'submitted')

  if (error) {
    throw new Error(error.message)
  }

  return data || []
}

// Update appraisal status (for approvals/rejections)
export async function updateAppraisalStatus(
  appraisalId: string, 
  status: 'approved' | 'rejected', 
  updatedBy: string, 
  reason?: string
): Promise<boolean> {
  const updateData: any = {
    status,
    updated_at: new Date().toISOString(),
  }

  if (status === 'approved') {
    updateData.approved_by = updatedBy
    updateData.approved_at = new Date().toISOString()
  } else if (status === 'rejected') {
    updateData.rejected_by = updatedBy
    updateData.rejected_at = new Date().toISOString()
    if (reason) {
      updateData.rejected_reason = reason
    }
  }

  const { error } = await supabaseAdmin.from('appy_appraisals')
    .update(updateData)
    .eq('id', appraisalId)

  if (error) {
    throw new Error(error.message)
  }

  return true
}