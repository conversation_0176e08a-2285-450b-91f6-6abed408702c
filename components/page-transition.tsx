"use client"

import { useEffect, useState } from "react"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { transitions, getAccessibleTransition } from "@/lib/transitions"
import { announceToScreenReader } from "@/lib/accessibility"

interface PageTransitionProps {
  children: React.ReactNode
  className?: string
}

export function PageTransition({ children, className }: PageTransitionProps) {
  const pathname = usePathname()
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [displayChildren, setDisplayChildren] = useState(children)

  useEffect(() => {
    console.log('[A11Y] Page transition started for:', pathname)
    setIsTransitioning(true)
    
    // Announce page change to screen readers
    const pageName = pathname.split('/').pop() || 'page'
    announceToScreenReader(`Navigating to ${pageName}`, "polite")

    // Small delay to allow transition to start
    const timer = setTimeout(() => {
      setDisplayChildren(children)
      setIsTransitioning(false)
      announceToScreenReader(`${pageName} loaded`, "polite")
    }, 150)

    return () => clearTimeout(timer)
  }, [pathname, children])

  return (
    <div 
      className={cn(
        "min-h-screen",
        getAccessibleTransition(transitions.fadeIn),
        {
          "opacity-0": isTransitioning,
          "opacity-100": !isTransitioning,
        },
        className
      )}
      role="main"
      aria-live="polite"
    >
      {displayChildren}
    </div>
  )
}

interface FadeTransitionProps {
  show: boolean
  children: React.ReactNode
  className?: string
  duration?: 'fast' | 'normal' | 'slow'
}

export function FadeTransition({ 
  show, 
  children, 
  className,
  duration = 'normal' 
}: FadeTransitionProps) {
  const [shouldRender, setShouldRender] = useState(show)

  useEffect(() => {
    if (show) {
      setShouldRender(true)
    } else {
      const timer = setTimeout(() => {
        setShouldRender(false)
      }, duration === 'fast' ? 150 : duration === 'slow' ? 300 : 200)
      
      return () => clearTimeout(timer)
    }
  }, [show, duration])

  if (!shouldRender) return null

  const durationClass = {
    fast: 'duration-150',
    normal: 'duration-200', 
    slow: 'duration-300'
  }[duration]

  return (
    <div
      className={cn(
        "transition-opacity ease-in-out",
        durationClass,
        getAccessibleTransition("transition-opacity"),
        {
          "opacity-0": !show,
          "opacity-100": show,
        },
        className
      )}
    >
      {children}
    </div>
  )
}

interface SlideTransitionProps {
  show: boolean
  children: React.ReactNode
  direction?: 'up' | 'down' | 'left' | 'right'
  className?: string
  duration?: 'fast' | 'normal' | 'slow'
}

export function SlideTransition({ 
  show, 
  children, 
  direction = 'up',
  className,
  duration = 'normal' 
}: SlideTransitionProps) {
  const [shouldRender, setShouldRender] = useState(show)

  useEffect(() => {
    if (show) {
      setShouldRender(true)
    } else {
      const timer = setTimeout(() => {
        setShouldRender(false)
      }, duration === 'fast' ? 150 : duration === 'slow' ? 300 : 200)
      
      return () => clearTimeout(timer)
    }
  }, [show, duration])

  if (!shouldRender) return null

  const durationClass = {
    fast: 'duration-150',
    normal: 'duration-200', 
    slow: 'duration-300'
  }[duration]

  const transformClasses = {
    up: show ? 'translate-y-0' : 'translate-y-4',
    down: show ? 'translate-y-0' : '-translate-y-4',
    left: show ? 'translate-x-0' : 'translate-x-4',
    right: show ? 'translate-x-0' : '-translate-x-4'
  }

  return (
    <div
      className={cn(
        "transition-all ease-in-out",
        durationClass,
        getAccessibleTransition("transition-all"),
        transformClasses[direction],
        {
          "opacity-0": !show,
          "opacity-100": show,
        },
        className
      )}
    >
      {children}
    </div>
  )
}

interface ScaleTransitionProps {
  show: boolean
  children: React.ReactNode
  className?: string
  duration?: 'fast' | 'normal' | 'slow'
}

export function ScaleTransition({ 
  show, 
  children, 
  className,
  duration = 'normal' 
}: ScaleTransitionProps) {
  const [shouldRender, setShouldRender] = useState(show)

  useEffect(() => {
    if (show) {
      setShouldRender(true)
    } else {
      const timer = setTimeout(() => {
        setShouldRender(false)
      }, duration === 'fast' ? 150 : duration === 'slow' ? 300 : 200)
      
      return () => clearTimeout(timer)
    }
  }, [show, duration])

  if (!shouldRender) return null

  const durationClass = {
    fast: 'duration-150',
    normal: 'duration-200', 
    slow: 'duration-300'
  }[duration]

  return (
    <div
      className={cn(
        "transition-all ease-in-out origin-center",
        durationClass,
        getAccessibleTransition("transition-all"),
        {
          "opacity-0 scale-95": !show,
          "opacity-100 scale-100": show,
        },
        className
      )}
    >
      {children}
    </div>
  )
}

interface StaggeredListProps {
  children: React.ReactNode[]
  show: boolean
  staggerDelay?: number
  className?: string
}

export function StaggeredList({ 
  children, 
  show, 
  staggerDelay = 50,
  className 
}: StaggeredListProps) {
  const [visibleItems, setVisibleItems] = useState<boolean[]>(
    new Array(children.length).fill(false)
  )

  useEffect(() => {
    if (show) {
      children.forEach((_, index) => {
        setTimeout(() => {
          setVisibleItems(prev => {
            const newState = [...prev]
            newState[index] = true
            return newState
          })
        }, index * staggerDelay)
      })
    } else {
      setVisibleItems(new Array(children.length).fill(false))
    }
  }, [show, children.length, staggerDelay])

  return (
    <div className={className}>
      {children.map((child, index) => (
        <FadeTransition key={index} show={visibleItems[index]}>
          {child}
        </FadeTransition>
      ))}
    </div>
  )
}

// Hook for managing transition states
export function useTransition(initialState = false) {
  const [isVisible, setIsVisible] = useState(initialState)
  const [isTransitioning, setIsTransitioning] = useState(false)

  const show = () => {
    console.log('[A11Y] Showing transition')
    setIsTransitioning(true)
    setIsVisible(true)
    setTimeout(() => setIsTransitioning(false), 200)
  }

  const hide = () => {
    console.log('[A11Y] Hiding transition')
    setIsTransitioning(true)
    setIsVisible(false)
    setTimeout(() => setIsTransitioning(false), 200)
  }

  const toggle = () => {
    if (isVisible) {
      hide()
    } else {
      show()
    }
  }

  return {
    isVisible,
    isTransitioning,
    show,
    hide,
    toggle
  }
}
