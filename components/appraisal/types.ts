import type { AppraisalDetails } from "@/lib/types"

// Common types for all appraisal form components
export type AppraisalFormField = keyof Omit<AppraisalDetails, "id" | "periodId" | "employeeId" | "managerId" | "status">

export type AppraisalFormData = Partial<AppraisalDetails>

export type AppraisalInputChangeHandler = (field: AppraisalFormField, value: any) => void

// Error types that match our validation requirements
export type FormError = {
  message?: string
}

export type AppraisalFormErrors = {
  q1?: FormError
  q2?: FormError
  q3?: FormError
  q4?: FormError
  q5?: FormError
  paymentStatus?: FormError
  keyContributions?: FormError
  extraInitiatives?: FormError
  performanceLacking?: FormError
  daysOffTaken?: FormError
  disciplineRating?: FormError
  disciplineComment?: FormError
  impactRating?: FormError
  impactComment?: FormError
  qualityRating?: FormError
  qualityComment?: FormError
  collaborationRating?: FormError
  collaborationComment?: FormError
  skillGrowthRating?: FormError
  skillGrowthComment?: FormError
  readinessPromotion?: FormError
  readinessComment?: FormError
  compensationRecommendation?: FormError
}