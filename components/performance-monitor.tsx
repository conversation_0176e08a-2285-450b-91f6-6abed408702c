"use client"

import { useEffect, useState } from 'react'
import { getPerformanceMonitor, detectMemoryLeaks, getPerformanceRecommendations } from '@/lib/performance'
import { log } from '@/lib/logging'
import { useMemoryLeakPrevention, usePerformanceMonitoring } from '@/hooks/use-memory-leak-prevention'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle, Activity, Zap, TrendingUp } from 'lucide-react'

interface PerformanceMonitorProps {
  showDetails?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
}

export function PerformanceMonitor({ 
  showDetails = false, 
  autoRefresh = true, 
  refreshInterval = 5000 
}: PerformanceMonitorProps) {
  const [summary, setSummary] = useState<any>(null)
  const [recommendations, setRecommendations] = useState<string[]>([])
  const [isVisible, setIsVisible] = useState(showDetails)
  
  const { safeSetInterval, cleanup } = useMemoryLeakPrevention('PerformanceMonitor')
  const { renderCount } = usePerformanceMonitoring('PerformanceMonitor')

  useEffect(() => {
    log.debug('Performance monitor component mounted', {}, 'PerformanceMonitor')
    
    const updateMetrics = () => {
      try {
        const monitor = getPerformanceMonitor()
        const currentSummary = monitor.getSummary()
        setSummary(currentSummary)
        
        const currentRecommendations = getPerformanceRecommendations()
        setRecommendations(currentRecommendations)
        
        // Detect memory leaks periodically
        detectMemoryLeaks()
        
        log.debug('Performance metrics updated', { summary: currentSummary }, 'PerformanceMonitor')
      } catch (error) {
        log.error('Failed to update performance metrics', error as Error, {}, 'PerformanceMonitor')
      }
    }

    // Initial update
    updateMetrics()

    // Set up auto-refresh
    let intervalId: NodeJS.Timeout | undefined
    if (autoRefresh) {
      intervalId = safeSetInterval(updateMetrics, refreshInterval)
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
      cleanup()
    }
  }, [autoRefresh, refreshInterval, safeSetInterval, cleanup])

  const toggleVisibility = () => {
    setIsVisible(!isVisible)
    log.userAction('toggle_performance_monitor', { visible: !isVisible }, 'PerformanceMonitor')
  }

  const clearMetrics = () => {
    try {
      const monitor = getPerformanceMonitor()
      monitor.clearMetrics()
      setSummary(null)
      setRecommendations([])
      log.userAction('clear_performance_metrics', {}, 'PerformanceMonitor')
    } catch (error) {
      log.error('Failed to clear performance metrics', error as Error, {}, 'PerformanceMonitor')
    }
  }

  if (!isVisible && !showDetails) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={toggleVisibility}
          className="bg-background/80 backdrop-blur-sm"
          aria-label="Show performance monitor"
        >
          <Activity className="h-4 w-4" />
          <span className="sr-only">Performance Monitor</span>
        </Button>
      </div>
    )
  }

  return (
    <div className={showDetails ? '' : 'fixed bottom-4 right-4 z-50 max-w-sm'}>
      <Card className={showDetails ? '' : 'bg-background/95 backdrop-blur-sm'}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              <CardTitle className="text-sm">Performance Monitor</CardTitle>
            </div>
            {!showDetails && (
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleVisibility}
                aria-label="Hide performance monitor"
              >
                ×
              </Button>
            )}
          </div>
          <CardDescription className="text-xs">
            Render #{renderCount} • {summary?.totalMetrics || 0} metrics
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Performance Metrics */}
          {summary && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-xs">
                <Zap className="h-3 w-3" />
                <span className="font-medium">Latest Metrics</span>
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-xs">
                {Object.entries(summary.latest).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="truncate">{key.replace(/_/g, ' ')}</span>
                    <Badge variant="outline" className="text-xs">
                      {typeof value === 'number' ? `${value.toFixed(0)}ms` : String(value)}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Performance Recommendations */}
          {recommendations.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-xs">
                <TrendingUp className="h-3 w-3" />
                <span className="font-medium">Recommendations</span>
              </div>
              
              <div className="space-y-1">
                {recommendations.slice(0, showDetails ? recommendations.length : 2).map((rec, index) => (
                  <Alert key={index} variant="default" className="py-2">
                    <AlertTriangle className="h-3 w-3" />
                    <AlertDescription className="text-xs">
                      {rec}
                    </AlertDescription>
                  </Alert>
                ))}
                
                {!showDetails && recommendations.length > 2 && (
                  <p className="text-xs text-muted-foreground">
                    +{recommendations.length - 2} more recommendations
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={clearMetrics}
              className="text-xs"
            >
              Clear
            </Button>
            
            {summary && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const monitor = getPerformanceMonitor()
                  const exportData = {
                    summary,
                    recommendations,
                    timestamp: new Date().toISOString()
                  }
                  
                  const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                    type: 'application/json'
                  })
                  
                  const url = URL.createObjectURL(blob)
                  const a = document.createElement('a')
                  a.href = url
                  a.download = `performance-report-${Date.now()}.json`
                  a.click()
                  URL.revokeObjectURL(url)
                  
                  log.userAction('export_performance_report', {}, 'PerformanceMonitor')
                }}
                className="text-xs"
              >
                Export
              </Button>
            )}
          </div>

          {/* Memory Usage Warning */}
          {summary?.latest.memory_used && summary.latest.memory_used > 50 * 1024 * 1024 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                High memory usage detected: {(summary.latest.memory_used / 1024 / 1024).toFixed(1)}MB
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// Development-only performance monitor
export function DevPerformanceMonitor() {
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return <PerformanceMonitor showDetails={false} autoRefresh={true} />
}

// Performance dashboard for admin users
export function PerformanceDashboard() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Performance Dashboard</h2>
        <p className="text-muted-foreground">
          Monitor application performance and identify optimization opportunities.
        </p>
      </div>
      
      <PerformanceMonitor showDetails={true} autoRefresh={true} refreshInterval={2000} />
    </div>
  )
}

console.log('[PERF] Performance monitor components loaded')
