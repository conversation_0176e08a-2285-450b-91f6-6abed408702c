import { getDepartments } from "@/lib/data/index"
import { getCurrentUser } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DepartmentsTable } from "@/components/departments-table"
import { RoleGuard } from "@/components/role-guard"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield } from "lucide-react"

export default async function DepartmentsPage() {
  const user = await getCurrentUser()

  if (!user) {
    return (
      <div className="p-4 sm:p-6">
        <Alert variant="destructive">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Authentication required. Please sign in to access this page.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <RoleGuard
      allowedRoles={['hr-admin', 'super-admin']}
      userRole={user.role}
      redirectTo="/dashboard"
    >
      <DepartmentManagementContent />
    </RoleGuard>
  )
}

async function DepartmentManagementContent() {
  const departments = await getDepartments()

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Department Management</h1>
        <p className="text-muted-foreground">Add, edit, and manage all company departments.</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>All Departments</CardTitle>
          <CardDescription>A list of all departments in the system.</CardDescription>
        </CardHeader>
        <CardContent>
          <DepartmentsTable data={departments} />
        </CardContent>
      </Card>
    </div>
  )
}
