"use client"

import * as React from "react"
import { flexRender, getCoreRowModel, useReactTable, type ColumnDef } from "@tanstack/react-table"
import { MoreHorizontal } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import type { AppraisalPeriod } from "@/lib/types"
import { PeriodFormDialog } from "./period-form-dialog"

export function PeriodsTable({ data }: { data: AppraisalPeriod[] }) {
  const [isFormOpen, setIsFormOpen] = React.useState(false)
  const [selectedPeriod, setSelectedPeriod] = React.useState<AppraisalPeriod | null>(null)

  const handleEdit = (period: AppraisalPeriod) => {
    setSelectedPeriod(period)
    setIsFormOpen(true)
  }

  const handleAddNew = () => {
    setSelectedPeriod(null)
    setIsFormOpen(true)
  }

  const handleFormClose = () => {
    setIsFormOpen(false)
    setTimeout(() => setSelectedPeriod(null), 300)
  }

  const columns: ColumnDef<AppraisalPeriod>[] = [
    {
      accessorKey: "periodStart",
      header: "Start Date",
      cell: ({ row }) => new Date(row.getValue("periodStart")).toLocaleDateString(),
    },
    {
      accessorKey: "periodEnd",
      header: "End Date",
      cell: ({ row }) => new Date(row.getValue("periodEnd")).toLocaleDateString(),
    },
    {
      accessorKey: "closed",
      header: "Status",
      cell: ({ row }) => {
        const isClosed = row.getValue("closed")
        return (
          <Badge variant={!isClosed ? "default" : "outline"} className={!isClosed ? "bg-blue-600" : ""}>
            {!isClosed ? "Open" : "Closed"}
          </Badge>
        )
      },
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEdit(row.original)}>Edit Period</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ]

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <div>
      <div className="flex items-center py-4">
        <Button onClick={handleAddNew} className="ml-auto">
          Open New Period
        </Button>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <PeriodFormDialog isOpen={isFormOpen} onClose={handleFormClose} period={selectedPeriod} />
    </div>
  )
}
