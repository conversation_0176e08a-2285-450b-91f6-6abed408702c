import { supabaseAdmin } from '../../supabase'

// Employee Feedback System Functions
export async function createFeedback(feedbackData: {
  submitterId: string
  targetEmployeeId?: string
  feedbackType: 'complaint' | 'suggestion' | 'recognition' | 'concern' | 'initiative' | 'general'
  category?: 'performance' | 'behavior' | 'communication' | 'teamwork' | 'leadership' | 'process' | 'other'
  subject: string
  message: string
  isAnonymous?: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
}): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_employee_feedback')
    .insert({
      submitter_id: feedbackData.submitterId,
      target_employee_id: feedbackData.targetEmployeeId || null,
      feedback_type: feedbackData.feedbackType,
      category: feedbackData.category || null,
      subject: feedbackData.subject,
      message: feedbackData.message,
      is_anonymous: feedbackData.isAnonymous || false,
      priority: feedbackData.priority || 'medium',
      status: 'pending'
    })
    .select()
    .single()

  if (error) {
    console.error('Failed to create feedback:', error)
    throw new Error(error.message)
  }

  return data
}

export async function getFeedback(filters?: {
  submitterId?: string
  targetEmployeeId?: string
  status?: string
  feedbackType?: string
  priority?: string
  limit?: number
}): Promise<any[]> {
  let query = supabaseAdmin
    .from('appy_employee_feedback')
    .select(`
      *,
      submitter:appy_employees!submitter_id (
        id,
        full_name,
        email
      ),
      target:appy_employees!target_employee_id (
        id,
        full_name,
        email
      ),
      reviewer:appy_managers!reviewed_by (
        user_id,
        full_name,
        email
      )
    `)
    .order('created_at', { ascending: false })

  if (filters?.submitterId) {
    query = query.eq('submitter_id', filters.submitterId)
  }

  if (filters?.targetEmployeeId) {
    query = query.eq('target_employee_id', filters.targetEmployeeId)
  }

  if (filters?.status) {
    query = query.eq('status', filters.status)
  }

  if (filters?.feedbackType) {
    query = query.eq('feedback_type', filters.feedbackType)
  }

  if (filters?.priority) {
    query = query.eq('priority', filters.priority)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  const { data, error } = await query

  if (error) {
    console.error('Failed to fetch feedback:', error)
    throw new Error(error.message)
  }

  return data || []
}

export async function getFeedbackById(feedbackId: string): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_employee_feedback')
    .select(`
      *,
      submitter:appy_employees!submitter_id (
        id,
        full_name,
        email
      ),
      target:appy_employees!target_employee_id (
        id,
        full_name,
        email
      ),
      reviewer:appy_managers!reviewed_by (
        user_id,
        full_name,
        email
      ),
      resolver:appy_managers!resolved_by (
        user_id,
        full_name,
        email
      )
    `)
    .eq('id', feedbackId)
    .single()

  if (error) {
    console.error('Failed to fetch feedback by ID:', error)
    throw new Error(error.message)
  }

  return data
}

export async function updateFeedbackStatus(feedbackId: string, updates: {
  status?: 'pending' | 'under_review' | 'investigating' | 'resolved' | 'closed' | 'escalated'
  reviewedBy?: string
  hrResponse?: string
  hrNotes?: string
  resolutionSummary?: string
  resolvedBy?: string
  requiresFollowup?: boolean
  followupDate?: string
  followupNotes?: string
}): Promise<any> {
  const updateData: any = {}

  if (updates.status) updateData.status = updates.status
  if (updates.reviewedBy) updateData.reviewed_by = updates.reviewedBy
  if (updates.hrResponse) updateData.hr_response = updates.hrResponse
  if (updates.hrNotes) updateData.hr_notes = updates.hrNotes
  if (updates.resolutionSummary) updateData.resolution_summary = updates.resolutionSummary
  if (updates.resolvedBy) updateData.resolved_by = updates.resolvedBy
  if (updates.requiresFollowup !== undefined) updateData.requires_followup = updates.requiresFollowup
  if (updates.followupDate) updateData.followup_date = updates.followupDate
  if (updates.followupNotes) updateData.followup_notes = updates.followupNotes

  if (updates.status === 'under_review' && updates.reviewedBy) {
    updateData.reviewed_at = new Date().toISOString()
  }

  if (updates.status === 'resolved' && updates.resolvedBy) {
    updateData.resolved_at = new Date().toISOString()
  }

  const { data, error } = await supabaseAdmin
    .from('appy_employee_feedback')
    .update(updateData)
    .eq('id', feedbackId)
    .select()
    .single()

  if (error) {
    console.error('Failed to update feedback status:', error)
    throw new Error(error.message)
  }

  return data
}

export async function addFeedbackComment(commentData: {
  feedbackId: string
  commenterId: string
  comment: string
  isInternal?: boolean
}): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_feedback_comments')
    .insert({
      feedback_id: commentData.feedbackId,
      commenter_id: commentData.commenterId,
      comment: commentData.comment,
      is_internal: commentData.isInternal || true
    })
    .select()
    .single()

  if (error) {
    console.error('Failed to add feedback comment:', error)
    throw new Error(error.message)
  }

  return data
}

export async function getFeedbackComments(feedbackId: string): Promise<any[]> {
  const { data, error } = await supabaseAdmin
    .from('appy_feedback_comments')
    .select(`
      *,
      commenter:appy_managers!commenter_id (
        user_id,
        full_name,
        email
      )
    `)
    .eq('feedback_id', feedbackId)
    .order('created_at', { ascending: true })

  if (error) {
    console.error('Failed to fetch feedback comments:', error)
    throw new Error(error.message)
  }

  return data || []
}

export async function getFeedbackStatistics(startDate?: string, endDate?: string): Promise<any> {
  const { data, error } = await supabaseAdmin
    .rpc('get_feedback_statistics', {
      start_date: startDate || null,
      end_date: endDate || null
    })

  if (error) {
    console.error('Failed to fetch feedback statistics:', error)
    throw new Error(error.message)
  }

  return data?.[0] || {
    total_feedback: 0,
    pending_count: 0,
    under_review_count: 0,
    resolved_count: 0,
    by_type: {},
    by_priority: {},
    avg_resolution_days: 0
  }
}

export async function getPendingFeedbackForHR(): Promise<any[]> {
  const { data, error } = await supabaseAdmin
    .rpc('get_pending_feedback_for_hr')

  if (error) {
    console.error('Failed to fetch pending feedback for HR:', error)
    throw new Error(error.message)
  }

  return data || []
}