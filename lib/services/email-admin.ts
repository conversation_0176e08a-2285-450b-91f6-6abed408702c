import { supabaseAdmin } from '../supabase'
import { debug } from '../debug'

export interface EmailSetting {
  id: string
  setting_key: string
  setting_value: string
  setting_type: 'string' | 'number' | 'boolean' | 'json'
  description?: string
  created_at: string
  updated_at: string
  updated_by?: string
}

export interface EmailTemplate {
  id: string
  template_key: string
  template_name: string
  subject_template: string
  html_template: string
  text_template: string
  available_variables: string[]
  is_active: boolean
  created_at: string
  updated_at: string
  updated_by?: string
}

export interface EmailConfigAudit {
  id: string
  action: 'create' | 'update' | 'delete' | 'test'
  table_name: string
  record_id: string
  old_values?: any
  new_values?: any
  changed_by?: string
  created_at: string
}

/**
 * Email admin service for managing email configuration
 */
export class EmailAdminService {
  private static instance: EmailAdminService

  private constructor() {}

  public static getInstance(): EmailAdminService {
    if (!EmailAdminService.instance) {
      EmailAdminService.instance = new EmailAdminService()
    }
    return EmailAdminService.instance
  }

  /**
   * Get all email settings
   */
  async getEmailSettings(): Promise<EmailSetting[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('appy_email_settings')
        .select('*')
        .order('setting_key')

      if (error) {
        console.error('Error fetching email settings:', error)
        throw error
      }

      return data || []
    } catch (error) {
      console.error('Error in getEmailSettings:', error)
      throw error
    }
  }

  /**
   * Get email setting by key
   */
  async getEmailSetting(key: string): Promise<EmailSetting | null> {
    try {
      const { data, error } = await supabaseAdmin
        .from('appy_email_settings')
        .select('*')
        .eq('setting_key', key)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return null // Not found
        }
        console.error('Error fetching email setting:', error)
        throw error
      }

      return data
    } catch (error) {
      console.error('Error in getEmailSetting:', error)
      throw error
    }
  }

  /**
   * Update email setting
   */
  async updateEmailSetting(
    key: string, 
    value: string, 
    userId: string,
    type: 'string' | 'number' | 'boolean' | 'json' = 'string'
  ): Promise<EmailSetting> {
    try {
      // Get current setting for audit
      const currentSetting = await this.getEmailSetting(key)
      
      const { data, error } = await supabaseAdmin
        .from('appy_email_settings')
        .upsert({
          setting_key: key,
          setting_value: value,
          setting_type: type,
          updated_at: new Date().toISOString(),
          updated_by: userId
        }, {
          onConflict: 'setting_key'
        })
        .select()
        .single()

      if (error) {
        console.error('Error updating email setting:', error)
        throw error
      }

      // Log audit record
      await this.logAuditRecord(
        currentSetting ? 'update' : 'create',
        'appy_email_settings',
        data.id,
        currentSetting || undefined,
        data,
        userId
      )

      debug.log(`✅ [EMAIL-ADMIN] Updated setting: ${key} = ${value}`)
      return data
    } catch (error) {
      console.error('Error in updateEmailSetting:', error)
      throw error
    }
  }

  /**
   * Get all email templates
   */
  async getEmailTemplates(): Promise<EmailTemplate[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('appy_email_templates')
        .select('*')
        .order('template_name')

      if (error) {
        console.error('Error fetching email templates:', error)
        throw error
      }

      return data || []
    } catch (error) {
      console.error('Error in getEmailTemplates:', error)
      throw error
    }
  }

  /**
   * Get email template by key
   */
  async getEmailTemplate(key: string): Promise<EmailTemplate | null> {
    try {
      const { data, error } = await supabaseAdmin
        .from('appy_email_templates')
        .select('*')
        .eq('template_key', key)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return null // Not found
        }
        console.error('Error fetching email template:', error)
        throw error
      }

      return data
    } catch (error) {
      console.error('Error in getEmailTemplate:', error)
      throw error
    }
  }

  /**
   * Update email template
   */
  async updateEmailTemplate(
    key: string,
    templateData: Partial<EmailTemplate>,
    userId: string
  ): Promise<EmailTemplate> {
    try {
      // Get current template for audit
      const currentTemplate = await this.getEmailTemplate(key)
      
      const { data, error } = await supabaseAdmin
        .from('appy_email_templates')
        .upsert({
          template_key: key,
          ...templateData,
          updated_at: new Date().toISOString(),
          updated_by: userId
        }, {
          onConflict: 'template_key'
        })
        .select()
        .single()

      if (error) {
        console.error('Error updating email template:', error)
        throw error
      }

      // Log audit record
      await this.logAuditRecord(
        currentTemplate ? 'update' : 'create',
        'appy_email_templates',
        data.id,
        currentTemplate || undefined,
        data,
        userId
      )

      debug.log(`✅ [EMAIL-ADMIN] Updated template: ${key}`)
      return data
    } catch (error) {
      console.error('Error in updateEmailTemplate:', error)
      throw error
    }
  }

  /**
   * Get email configuration audit log
   */
  async getAuditLog(limit: number = 50): Promise<EmailConfigAudit[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('appy_email_config_audit')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('Error fetching audit log:', error)
        throw error
      }

      return data || []
    } catch (error) {
      console.error('Error in getAuditLog:', error)
      throw error
    }
  }

  /**
   * Test email configuration
   */
  async testEmailConfiguration(userId: string): Promise<boolean> {
    try {
      // Get test email setting
      const testEmailSetting = await this.getEmailSetting('test_email')
      if (!testEmailSetting) {
        throw new Error('Test email address not configured')
      }

      // Validate test email domain for security
      const allowedDomains = process.env.ALLOWED_TEST_EMAIL_DOMAINS?.split(',') || ['@yourcompany.com', '@test.com']
      const testEmail = testEmailSetting.setting_value.toLowerCase()
      const isAllowedDomain = allowedDomains.some(domain => testEmail.endsWith(domain.toLowerCase()))
      
      if (!isAllowedDomain) {
        throw new Error(`Test email must be from allowed domains: ${allowedDomains.join(', ')}`)
      }

      // Get email template
      const template = await this.getEmailTemplate('appraisal_reminder')
      if (!template) {
        throw new Error('Email template not found')
      }

      // Get manager info for test
      const { data: manager, error: managerError } = await supabaseAdmin
        .from('appy_managers')
        .select('user_id, full_name, email')
        .eq('user_id', userId)
        .single()

      if (managerError || !manager) {
        throw new Error('Manager profile not found')
      }

      // Create test notification data
      const testData = {
        user_id: userId,
        manager_name: manager.full_name,
        manager_email: testEmailSetting.setting_value,
        period_end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        pending_appraisals: [
          {
            employee_id: 'test-1',
            employee_name: 'John Doe (Test)',
            department_name: 'Engineering',
            days_remaining: 4
          },
          {
            employee_id: 'test-2',
            employee_name: 'Jane Smith (Test)',
            department_name: 'Marketing',
            days_remaining: 4
          }
        ]
      }

      // Import and use email service
      const { emailService } = await import('./email')
      const success = await emailService.sendAppraisalReminder(testData)

      // Log test action
      await this.logAuditRecord(
        'test',
        'email_system',
        'test-email',
        undefined,
        { 
          test_email: testEmailSetting.setting_value,
          success,
          timestamp: new Date().toISOString()
        },
        userId
      )

      debug.log(`✅ [EMAIL-ADMIN] Test email sent: ${success}`)
      return success
    } catch (error) {
      console.error('Error in testEmailConfiguration:', error)
      throw error
    }
  }

  /**
   * Get email system status
   */
  async getEmailSystemStatus(): Promise<{
    configured: boolean
    lastSent?: string
    recentFailures: number
    queuedNotifications: number
  }> {
    try {
      // Check if basic settings are configured
      const fromEmail = await this.getEmailSetting('from_email')
      const configured = !!fromEmail && fromEmail.setting_value !== '<EMAIL>'

      // Get recent email log
      const { data: recentEmails, error: logError } = await supabaseAdmin
        .from('appy_notification_log')
        .select('email_sent, email_sent_at, email_error')
        .order('created_at', { ascending: false })
        .limit(50)

      if (logError) {
        console.error('Error fetching email log:', logError)
      }

      // Calculate metrics
      const now = new Date()
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      
      const recentFailures = recentEmails?.filter(email => {
        if (email.email_sent) return false
        if (!email.email_sent_at) return false
        try {
          const emailDate = new Date(email.email_sent_at)
          return !isNaN(emailDate.getTime()) && emailDate > oneDayAgo
        } catch {
          return false
        }
      }).length || 0

      const lastSentEmail = recentEmails?.find(email => email.email_sent)
      const lastSent = lastSentEmail?.email_sent_at || undefined

      // Get queued notifications
      const { data: queuedNotifications, error: queueError } = await supabaseAdmin
        .from('appy_scheduled_notifications')
        .select('id')
        .eq('processed', false)
        .lte('scheduled_for', now.toISOString())

      if (queueError) {
        console.error('Error fetching queued notifications:', queueError)
      }

      return {
        configured,
        lastSent,
        recentFailures,
        queuedNotifications: queuedNotifications?.length || 0
      }
    } catch (error) {
      console.error('Error in getEmailSystemStatus:', error)
      throw error
    }
  }

  /**
   * Log audit record
   */
  private async logAuditRecord(
    action: 'create' | 'update' | 'delete' | 'test',
    tableName: string,
    recordId: string,
    oldValues?: any,
    newValues?: any,
    userId?: string
  ): Promise<void> {
    try {
      await supabaseAdmin
        .from('appy_email_config_audit')
        .insert({
          action,
          table_name: tableName,
          record_id: recordId,
          old_values: oldValues || null,
          new_values: newValues || null,
          changed_by: userId || null
        })
    } catch (error) {
      console.error('Error logging audit record:', error)
      // Don't throw here as it's not critical for the main operation
    }
  }
}

// Export singleton instance
export const emailAdminService = EmailAdminService.getInstance()

// Console log for debugging
debug.log('📧 Email admin service initialized')