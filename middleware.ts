import { clerkMiddleware } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import { adminMiddleware, isAdminRoute } from './lib/admin-middleware'

export default clerkMiddleware(async (auth, req) => {
  // Create response
  const response = NextResponse.next()

  // Clear any accumulated Clerk handshake parameters from URL
  const url = new URL(req.url)
  if (url.searchParams.has('__clerk_handshake')) {
    url.searchParams.delete('__clerk_handshake')
    return NextResponse.redirect(url)
  }

  // Apply admin middleware for admin routes
  if (isAdminRoute(url.pathname)) {
    return await adminMiddleware(req, auth)
  }

  return response
})

export const config = {
  matcher: [
    '/((?!_next/image|_next/static|favicon.ico|.*\\..*).*)' 
  ],
}