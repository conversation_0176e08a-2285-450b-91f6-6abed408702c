import { Suspense } from "react"
import { auth } from "@clerk/nextjs/server"
import { db } from "@/lib/db"
import { FeedbackForm } from "@/components/feedback-form"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { MessageSquare, Clock, CheckCircle, AlertTriangle } from "lucide-react"
import { redirect } from "next/navigation"

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  under_review: 'bg-blue-100 text-blue-800',
  investigating: 'bg-purple-100 text-purple-800',
  resolved: 'bg-green-100 text-green-800',
  closed: 'bg-gray-100 text-gray-800',
  escalated: 'bg-red-100 text-red-800'
}

const typeIcons = {
  complaint: AlertTriangle,
  suggestion: MessageSquare,
  recognition: CheckCircle,
  concern: Clock,
  initiative: MessageSquare,
  general: MessageSquare
}

async function FeedbackHistory({ employeeId }: { employeeId: string }) {
  const feedback = await db.getFeedback({ submitterId: employeeId })

  if (feedback.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Your Feedback History</CardTitle>
          <CardDescription>
            View the status of your submitted feedback
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No feedback submitted yet</h3>
            <p className="text-muted-foreground">
              Your submitted feedback will appear here with status updates.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Your Feedback History</CardTitle>
        <CardDescription>
          Track the status of your submitted feedback
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {feedback.map((item) => {
            const TypeIcon = typeIcons[item.feedback_type as keyof typeof typeIcons] || MessageSquare
            return (
              <div key={item.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <TypeIcon className="h-4 w-4 text-muted-foreground" />
                    <h4 className="font-medium">{item.subject}</h4>
                  </div>
                  <Badge
                    variant="outline"
                    className={statusColors[item.status as keyof typeof statusColors]}
                  >
                    {item.status.replace(/_/g, ' ')}
                  </Badge>
                </div>

                <div className="text-sm text-muted-foreground mb-2">
                  <span className="capitalize">{item.feedback_type}</span>
                  {item.target?.full_name && (
                    <span> • About: {item.target.full_name}</span>
                  )}
                  <span> • {new Date(item.created_at).toLocaleDateString()}</span>
                </div>

                <p className="text-sm line-clamp-2 mb-3">
                  {item.message}
                </p>

                {item.hr_response && (
                  <div className="bg-blue-50 border border-blue-200 rounded p-3">
                    <h5 className="font-medium text-blue-900 mb-1">HR Response:</h5>
                    <p className="text-sm text-blue-800">{item.hr_response}</p>
                    {item.reviewed_at && (
                      <p className="text-xs text-blue-600 mt-1">
                        Reviewed on {new Date(item.reviewed_at).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

export default async function FeedbackPage() {
  const { userId } = await auth()

  if (!userId) {
    redirect('/sign-in')
  }

  // Get current user's employee record
  const currentEmployee = await db.getEmployeeByClerkId(userId)
  if (!currentEmployee) {
    redirect('/sign-in')
  }

  // Get employees for the feedback form
  const employeesData = await db.getEmployees()
  const employees = employeesData.map(emp => ({
    id: emp.id,
    fullName: emp.full_name,
    departmentName: emp.department_name || 'No Department'
  }))

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex flex-col gap-4">
        <h1 className="text-3xl font-bold">Employee Feedback</h1>
        <p className="text-muted-foreground">
          Share your feedback, suggestions, or concerns with HR. All submissions are treated confidentially.
        </p>
      </div>

      <div className="grid gap-8 lg:grid-cols-2">
        {/* Feedback Form */}
        <div>
          <FeedbackForm employees={employees} />
        </div>

        {/* Feedback History */}
        <div>
          <Suspense fallback={
            <Card>
              <CardHeader>
                <CardTitle>Your Feedback History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="animate-pulse space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="border rounded-lg p-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-full"></div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          }>
            <FeedbackHistory employeeId={currentEmployee.id} />
          </Suspense>
        </div>
      </div>

      {/* Information Section */}
      <Card>
        <CardHeader>
          <CardTitle>How Feedback Works</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <MessageSquare className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-medium mb-2">Submit Feedback</h3>
              <p className="text-sm text-muted-foreground">
                Share your thoughts, concerns, or suggestions using the form above.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
              <h3 className="font-medium mb-2">HR Review</h3>
              <p className="text-sm text-muted-foreground">
                HR will review your feedback and investigate if necessary.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-medium mb-2">Resolution</h3>
              <p className="text-sm text-muted-foreground">
                You'll receive updates on the status and resolution of your feedback.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
