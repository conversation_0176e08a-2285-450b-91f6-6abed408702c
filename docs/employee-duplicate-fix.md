# Employee Duplicate Creation Bug Fix

## Problem Description

When editing an employee in the system, instead of updating the existing record, a new duplicate employee was being created. This was causing:

- Multiple employees with the same name
- Data inconsistency 
- Confusion in the employee list
- Potential issues with appraisals and relationships

## Root Cause Analysis

The issue was caused by a **unique constraint violation** on the `email` field in the `appy_employees` table:

1. **Unique Constraint**: The database has a `unique_employee_email` constraint
2. **Update Logic Flaw**: When updating an employee's email, the system was trying to set the same email that already existed
3. **Error Handling**: The unique constraint error wasn't being handled properly
4. **Fallback Behavior**: The system was silently falling back to creating a new employee instead of updating

## Database Investigation

```sql
-- Found unique constraint on email field
SELECT conname, contype, pg_get_constraintdef(oid) as definition 
FROM pg_constraint 
WHERE conrelid = 'appy_employees'::regclass;

-- Result showed: unique_employee_email UNIQUE (email)
```

## Solution Implemented

### 1. **Cleaned Up Existing Duplicates**
- Removed duplicate employees with temporary email addresses
- Kept employees with real email addresses

### 2. **Enhanced Email Uniqueness Checking**
- Added `isEmailUnique()` helper function
- Checks email uniqueness before create/update operations
- Excludes current employee ID when checking for updates

### 3. **Improved Error Handling**
- Added specific error messages for unique constraint violations
- Better debugging with conditional console logging
- Proper error propagation to the UI

### 4. **Enhanced Update Logic**
- Pre-validation of email uniqueness before database operations
- Better error messages for different constraint violations
- Verification that employee exists before updating

## Code Changes

### New Helper Function
```typescript
// lib/db/domains/employees.ts
export async function isEmailUnique(email: string, excludeId?: string): Promise<boolean> {
  const query = supabaseAdmin.from('appy_employees')
    .select('id')
    .eq('email', email)
    .limit(1)
  
  if (excludeId) {
    query.neq('id', excludeId)
  }
  
  const { data, error } = await query
  return data.length === 0
}
```

### Enhanced Update Function
```typescript
export async function updateEmployee(id: string, employeeData: {...}): Promise<Employee> {
  // Check email uniqueness if email is being updated
  if (employeeData.email !== undefined) {
    const emailIsUnique = await isEmailUnique(employeeData.email, id)
    if (!emailIsUnique) {
      throw new Error('An employee with this email address already exists')
    }
  }
  
  // ... rest of update logic with better error handling
}
```

### Improved Save Function
```typescript
export async function saveEmployee(emp: Partial<Employee>): Promise<void> {
  if (emp.id) {
    // Verify employee exists before updating
    const existingEmployee = await db.getEmployeeById(emp.id)
    if (!existingEmployee) {
      throw new Error(`Employee with ID ${emp.id} not found`)
    }
    
    await db.updateEmployee(emp.id, {...})
  } else {
    await db.createEmployee({...})
  }
}
```

## Testing

1. **Build Verification**: ✅ `bun run build` completed successfully
2. **Database Cleanup**: ✅ Removed duplicate Francesco entries
3. **Constraint Validation**: ✅ Email uniqueness properly enforced

## Prevention Measures

1. **Pre-validation**: Email uniqueness checked before database operations
2. **Better Error Messages**: Users get clear feedback about constraint violations
3. **Debug Logging**: Conditional logging for troubleshooting (controlled by `ENABLE_DEBUG_LOGS`)
4. **Existence Verification**: Employee existence verified before updates

## Environment Configuration

To enable debug logging for troubleshooting:
```bash
# .env.local
ENABLE_DEBUG_LOGS=true
```

## Database Schema

The unique constraint that caused the issue:
```sql
ALTER TABLE appy_employees 
ADD CONSTRAINT unique_employee_email UNIQUE (email);
```

This constraint is necessary for data integrity but required proper handling in the application logic.

## Result

- ✅ **No more duplicate employees** when editing
- ✅ **Clear error messages** for constraint violations  
- ✅ **Proper update vs create logic**
- ✅ **Better debugging capabilities**
- ✅ **Data integrity maintained**

The employee edit functionality now works correctly, updating existing records instead of creating duplicates.
