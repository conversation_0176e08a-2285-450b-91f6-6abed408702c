import { supabaseAdmin } from '../../supabase'
import { debug } from '../../debug'
import type { UserRole } from '../../schemas'

// User Roles
export async function getUserRole(userId: string): Promise<UserRole | null> {
  debug.log('🔍 Getting user role for:', userId)

  const { data: userRole, error } = await supabaseAdmin.from('appy_user_roles')
    .select('user_id, role, created_at')
    .eq('user_id', userId)
    .single()

  if (error) {
    debug.log('❌ User role not found for:', userId)
    return null
  }

  debug.log('✅ Found user role:', userRole)
  return userRole.role as UserRole
}

export async function createUserRole(userId: string, role: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'): Promise<UserRole> {
  debug.log('🔄 Creating user role:', { userId, role })

  const { data, error } = await supabaseAdmin.from('appy_user_roles')
    .insert({
      user_id: userId,
      role: role
    })
    .select()
    .single()

  if (error) {
    console.error('❌ Failed to create user role:', error)
    throw new Error(error.message)
  }

  debug.log('✅ User role created successfully')
  return data
}

export async function updateUserRole(userId: string, role: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'): Promise<UserRole> {
  debug.log('🔄 Updating user role:', { userId, role })

  const { data, error } = await supabaseAdmin.from('appy_user_roles')
    .update({ role })
    .eq('user_id', userId)
    .select()
    .single()

  if (error) {
    console.error('❌ Failed to update user role:', error)
    throw new Error(error.message)
  }

  debug.log('✅ User role updated successfully')
  return data
}