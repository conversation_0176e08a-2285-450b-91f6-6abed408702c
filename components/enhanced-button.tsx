"use client"

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { transitions, getAccessibleTransition } from "@/lib/transitions"
import { announceToScreenReader } from "@/lib/accessibility"

const enhancedButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
      loading: {
        true: "cursor-wait",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      loading: false,
    },
  }
)

export interface EnhancedButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof enhancedButtonVariants> {
  asChild?: boolean
  loading?: boolean
  loadingText?: string
  successText?: string
  errorText?: string
  state?: 'idle' | 'loading' | 'success' | 'error'
  onStateChange?: (state: 'idle' | 'loading' | 'success' | 'error') => void
}

const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    asChild = false, 
    loading = false,
    loadingText,
    successText,
    errorText,
    state = 'idle',
    onStateChange,
    children,
    onClick,
    disabled,
    ...props 
  }, ref) => {
    const [internalState, setInternalState] = React.useState<'idle' | 'loading' | 'success' | 'error'>(state)
    const [isPressed, setIsPressed] = React.useState(false)
    
    // Sync internal state with prop
    React.useEffect(() => {
      setInternalState(state)
    }, [state])

    // Announce state changes to screen readers
    React.useEffect(() => {
      switch (internalState) {
        case 'loading':
          announceToScreenReader(loadingText || "Loading", "polite")
          break
        case 'success':
          announceToScreenReader(successText || "Success", "polite")
          break
        case 'error':
          announceToScreenReader(errorText || "Error occurred", "assertive")
          break
      }
    }, [internalState, loadingText, successText, errorText])

    const isLoading = loading || internalState === 'loading'
    const isDisabled = disabled || isLoading

    const handleClick = React.useCallback(async (event: React.MouseEvent<HTMLButtonElement>) => {
      if (isDisabled) return

      console.log('[A11Y] Enhanced button clicked, current state:', internalState)
      
      if (onClick) {
        try {
          setInternalState('loading')
          onStateChange?.('loading')
          
          await onClick(event)
          
          setInternalState('success')
          onStateChange?.('success')
          
          // Reset to idle after success
          setTimeout(() => {
            setInternalState('idle')
            onStateChange?.('idle')
          }, 2000)
        } catch (error) {
          console.error('[A11Y] Button action failed:', error)
          setInternalState('error')
          onStateChange?.('error')
          
          // Reset to idle after error
          setTimeout(() => {
            setInternalState('idle')
            onStateChange?.('idle')
          }, 3000)
        }
      }
    }, [onClick, isDisabled, internalState, onStateChange])

    const handleMouseDown = () => setIsPressed(true)
    const handleMouseUp = () => setIsPressed(false)
    const handleMouseLeave = () => setIsPressed(false)

    const getButtonContent = () => {
      switch (internalState) {
        case 'loading':
          return (
            <>
              <Loader2 className="h-4 w-4 animate-spin" aria-hidden="true" />
              {loadingText || children}
            </>
          )
        case 'success':
          return successText || children
        case 'error':
          return errorText || children
        default:
          return children
      }
    }

    const getAriaLabel = () => {
      switch (internalState) {
        case 'loading':
          return `${loadingText || 'Loading'}, please wait`
        case 'success':
          return successText || 'Action completed successfully'
        case 'error':
          return errorText || 'Action failed, please try again'
        default:
          return props['aria-label']
      }
    }

    const buttonClasses = cn(
      enhancedButtonVariants({ variant, size, loading: isLoading, className }),
      getAccessibleTransition(transitions.button),
      {
        'scale-95': isPressed && !isDisabled,
        'opacity-75': internalState === 'loading',
        'bg-green-600 hover:bg-green-700': internalState === 'success' && variant === 'default',
        'bg-red-600 hover:bg-red-700': internalState === 'error' && variant === 'default',
      }
    )

    const Comp = asChild ? Slot : "button"
    
    return (
      <Comp
        className={buttonClasses}
        ref={ref}
        disabled={isDisabled}
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        aria-label={getAriaLabel()}
        aria-busy={isLoading}
        aria-live="polite"
        {...props}
      >
        {getButtonContent()}
      </Comp>
    )
  }
)
EnhancedButton.displayName = "EnhancedButton"

export { EnhancedButton, enhancedButtonVariants }

// Hook for managing button states
export function useButtonState(initialState: 'idle' | 'loading' | 'success' | 'error' = 'idle') {
  const [state, setState] = React.useState(initialState)
  
  const setLoading = React.useCallback(() => {
    console.log('[A11Y] Button state changed to loading')
    setState('loading')
  }, [])
  
  const setSuccess = React.useCallback(() => {
    console.log('[A11Y] Button state changed to success')
    setState('success')
  }, [])
  
  const setError = React.useCallback(() => {
    console.log('[A11Y] Button state changed to error')
    setState('error')
  }, [])
  
  const setIdle = React.useCallback(() => {
    console.log('[A11Y] Button state changed to idle')
    setState('idle')
  }, [])
  
  const reset = React.useCallback(() => {
    console.log('[A11Y] Button state reset to idle')
    setState('idle')
  }, [])
  
  return {
    state,
    setLoading,
    setSuccess,
    setError,
    setIdle,
    reset,
    isLoading: state === 'loading',
    isSuccess: state === 'success',
    isError: state === 'error',
    isIdle: state === 'idle'
  }
}
