"use client"

import { <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card"
import { RotateCcw, Clock } from "lucide-react"
import type { AppraisalDetails, EmployeeDetails } from "@/lib/types"

type AppraisalHeaderProps = {
  employee: EmployeeDetails
  appraisal: AppraisalDetails | null
}

export function AppraisalHeader({ employee, appraisal }: AppraisalHeaderProps) {
  return (
    <CardHeader>
      <div className="flex items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            Monthly Review
            {appraisal?.isRevision && (
              <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full">
                <RotateCcw className="h-3 w-3" />
                Revision {appraisal.revisionNumber}
              </span>
            )}
          </CardTitle>
          <CardDescription>
            Fill out the form below for {employee.fullName}. Your progress is saved as a draft automatically.
            {appraisal?.isRevision && appraisal.originalSubmissionDate && (
              <div className="flex items-center gap-1 mt-1 text-sm text-muted-foreground">
                <Clock className="h-3 w-3" />
                Originally submitted: {appraisal.originalSubmissionDate.split('T')[0]}
              </div>
            )}
          </CardDescription>
        </div>
      </div>
    </CardHeader>
  )
}