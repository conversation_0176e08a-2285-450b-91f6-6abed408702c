"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  MoreHorizontal,
  Eye,
  UserCheck,
  AlertTriangle,
  TrendingUp,
  Calendar
} from "lucide-react"
import { toast } from "sonner"
import type { PendingApproval } from "@/lib/types"

interface MultiLevelApprovalDashboardProps {
  pendingApprovals: PendingApproval[]
  statistics: {
    totalPending: number
    totalInProgress: number
    totalCompleted: number
    avgApprovalTime: number
  }
}

const priorityColors = {
  1: 'bg-red-100 text-red-800',
  2: 'bg-orange-100 text-orange-800',
  3: 'bg-blue-100 text-blue-800'
}

export function MultiLevelApprovalDashboard({ pendingApprovals, statistics }: MultiLevelApprovalDashboardProps) {
  const router = useRouter()
  const [selectedApproval, setSelectedApproval] = useState<PendingApproval | null>(null)
  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = useState(false)
  const [approvalAction, setApprovalAction] = useState<'approve' | 'reject' | null>(null)
  const [comments, setComments] = useState('')
  const [rejectionReason, setRejectionReason] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)

  const handleApprovalAction = async (approval: PendingApproval, action: 'approve' | 'reject') => {
    setSelectedApproval(approval)
    setApprovalAction(action)
    setComments('')
    setRejectionReason('')
    setIsApprovalDialogOpen(true)
  }

  const processApproval = async () => {
    if (!selectedApproval || !approvalAction) return

    setIsProcessing(true)
    try {
      const response = await fetch('/api/approvals/process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          stepId: selectedApproval.stepId,
          action: approvalAction === 'approve' ? 'approved' : 'rejected',
          comments: comments || undefined,
          rejectionReason: approvalAction === 'reject' ? rejectionReason : undefined
        })
      })

      const result = await response.json()

      if (result.success) {
        toast.success(
          approvalAction === 'approve' 
            ? 'Appraisal approved successfully!' 
            : 'Appraisal rejected successfully!'
        )
        setIsApprovalDialogOpen(false)
        // Refresh the page to update the list
        router.refresh()
      } else {
        toast.error(result.error || 'Failed to process approval')
      }
    } catch (error) {
      console.error('Error processing approval:', error)
      toast.error('An unexpected error occurred')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleViewAppraisal = (appraisalId: string) => {
    router.push(`/dashboard/appraisals/${appraisalId}`)
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{statistics.totalPending}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting your approval
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{statistics.totalInProgress}</div>
            <p className="text-xs text-muted-foreground">
              Multi-level approvals
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{statistics.totalCompleted}</div>
            <p className="text-xs text-muted-foreground">
              Fully approved
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Approval Time</CardTitle>
            <Calendar className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {statistics.avgApprovalTime.toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">
              Days to complete
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Pending Approvals Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Pending Approvals
          </CardTitle>
          <CardDescription>
            Appraisals awaiting your approval in the multi-level workflow
          </CardDescription>
        </CardHeader>
        <CardContent>
          {pendingApprovals.length === 0 ? (
            <div className="text-center py-12">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">All caught up!</h3>
              <p className="text-muted-foreground">
                No pending approvals require your attention at this time.
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Level</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead>Days Pending</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingApprovals.map((approval) => {
                    const isUrgent = approval.daysPending > 7
                    const isOverdue = approval.daysPending > 14

                    return (
                      <TableRow key={approval.stepId}>
                        <TableCell className="font-medium">
                          {approval.employeeName}
                        </TableCell>
                        <TableCell>{approval.departmentName}</TableCell>
                        <TableCell>
                          <Badge 
                            variant="outline" 
                            className={priorityColors[approval.stepLevel as keyof typeof priorityColors] || 'bg-gray-100 text-gray-800'}
                          >
                            Level {approval.stepLevel}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(approval.submissionDate).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className={isOverdue ? 'text-red-600 font-medium' : isUrgent ? 'text-orange-600' : ''}>
                              {approval.daysPending} days
                            </span>
                            {isOverdue && <AlertTriangle className="h-4 w-4 text-red-600" />}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center gap-2 justify-end">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleViewAppraisal(approval.appraisalId)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-green-600 hover:text-green-700"
                              onClick={() => handleApprovalAction(approval, 'approve')}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-red-600 hover:text-red-700"
                              onClick={() => handleApprovalAction(approval, 'reject')}
                            >
                              <XCircle className="h-4 w-4 mr-1" />
                              Reject
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Approval Dialog */}
      <Dialog open={isApprovalDialogOpen} onOpenChange={setIsApprovalDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {approvalAction === 'approve' ? 'Approve' : 'Reject'} Appraisal
            </DialogTitle>
            <DialogDescription>
              {selectedApproval && (
                <>
                  {approvalAction === 'approve' ? 'Approve' : 'Reject'} the appraisal for{' '}
                  <strong>{selectedApproval.employeeName}</strong> at approval level{' '}
                  <strong>{selectedApproval.stepLevel}</strong>.
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {approvalAction === 'reject' && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Rejection Reason *</label>
                <Textarea
                  placeholder="Please provide a reason for rejection..."
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  rows={3}
                />
              </div>
            )}

            <div className="space-y-2">
              <label className="text-sm font-medium">
                Comments {approvalAction === 'reject' ? '(Optional)' : ''}
              </label>
              <Textarea
                placeholder={
                  approvalAction === 'approve' 
                    ? "Add any comments about this approval..." 
                    : "Additional comments..."
                }
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsApprovalDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button 
              onClick={processApproval}
              disabled={isProcessing || (approvalAction === 'reject' && !rejectionReason.trim())}
              className={approvalAction === 'approve' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
            >
              {isProcessing ? 'Processing...' : (approvalAction === 'approve' ? 'Approve' : 'Reject')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
