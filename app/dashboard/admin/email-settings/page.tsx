import { requireRole } from '@/lib/auth'
import { EmailSettingsDashboard } from '@/components/email-settings-dashboard'
import { redirect } from 'next/navigation'

export default async function EmailSettingsPage() {
  // Require super-admin role
  try {
    const currentUser = await requireRole(['super-admin'])
    
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Email System Settings</h1>
          <p className="text-muted-foreground">
            Configure email settings, templates, and test email functionality
          </p>
        </div>
        
        <EmailSettingsDashboard currentUser={currentUser} />
      </div>
    )
  } catch (error) {
    console.error('Email settings page access denied:', error)
    redirect('/dashboard?error=access_denied')
  }
}

export const metadata = {
  title: 'Email Settings - Admin Dashboard',
  description: 'Configure email system settings and templates',
}