'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { TestTube, Mail, RefreshCw, CheckCircle, AlertCircle, Clock, Send, Loader2 } from 'lucide-react'

interface EmailSettingValue {
  value: string | number | boolean
  type: 'string' | 'number' | 'boolean'
}

interface EmailSettings {
  from_email?: EmailSettingValue
  from_name?: EmailSettingValue
  test_email?: EmailSettingValue
  notifications_enabled?: EmailSettingValue
  [key: string]: EmailSettingValue | undefined
}

interface EmailTemplate {
  id: string
  name: string
  subject: string
  content: string
}

interface Props {
  settings: EmailSettings
  templates: EmailTemplate[]
  systemStatus: {
    configured: boolean
    lastSent?: string
    recentFailures: number
    queuedNotifications: number
  }
  onTestEmail: () => Promise<void>
  onRefresh: () => Promise<void>
}

export function EmailTestPanel({ settings, templates, systemStatus, onTestEmail, onRefresh }: Props) {
  const [testing, setTesting] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  const handleTestEmail = async () => {
    setTesting(true)
    try {
      await onTestEmail()
    } catch (error) {
      console.error('Failed to send test email:', error)
      // Consider adding a toast notification here to inform the user
    } finally {
      setTesting(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      await onRefresh()
    } catch (error) {
      console.error('Failed to refresh system status:', error)
      // Consider adding a toast notification here to inform the user
    } finally {
      setRefreshing(false)
    }
  }

  const getStatusColor = (status: boolean) => {
    return status ? 'text-green-600' : 'text-red-600'
  }

  const getStatusIcon = (status: boolean) => {
    return status ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />
  }

  return (
    <div className="space-y-6">
      {/* Test Email */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Test Email Configuration
          </CardTitle>
          <CardDescription>
            Send a test email to verify your configuration is working correctly
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
            <div className="space-y-1">
              <p className="text-sm font-medium">Test Email Address</p>
              <p className="text-sm text-muted-foreground">
                {settings.test_email?.value || 'Not configured'}
              </p>
            </div>
            <Button onClick={handleTestEmail} disabled={testing || !settings.test_email?.value}>
              {testing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Test Email
                </>
              )}
            </Button>
          </div>
          
          <div className="text-sm text-muted-foreground">
            <p>
              This will send a sample appraisal reminder email using your current template 
              and settings. Check the configured test email address to verify delivery.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Configuration Status
          </CardTitle>
          <CardDescription>
            Current status of email system configuration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">From Email</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(!!settings.from_email?.value)}
                  <span className={`text-sm ${getStatusColor(!!settings.from_email?.value)}`}>
                    {settings.from_email?.value ? 'Configured' : 'Missing'}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">From Name</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(!!settings.from_name?.value)}
                  <span className={`text-sm ${getStatusColor(!!settings.from_name?.value)}`}>
                    {settings.from_name?.value ? 'Configured' : 'Missing'}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Test Email</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(!!settings.test_email?.value)}
                  <span className={`text-sm ${getStatusColor(!!settings.test_email?.value)}`}>
                    {settings.test_email?.value ? 'Configured' : 'Missing'}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Notifications</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(!!settings.notifications_enabled?.value)}
                  <span className={`text-sm ${getStatusColor(!!settings.notifications_enabled?.value)}`}>
                    {settings.notifications_enabled?.value === true ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Email Templates</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(templates.length > 0)}
                  <span className={`text-sm ${getStatusColor(templates.length > 0)}`}>
                    {templates.length > 0 ? `${templates.length} templates` : 'No templates'}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">API Connection</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(systemStatus.configured)}
                  <span className={`text-sm ${getStatusColor(systemStatus.configured)}`}>
                    {systemStatus.configured ? 'Connected' : 'Not configured'}
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Overall Status</span>
            <Badge variant={systemStatus.configured ? 'default' : 'destructive'}>
              {systemStatus.configured ? 'Ready' : 'Needs Configuration'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* System Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            System Metrics
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleRefresh}
              disabled={refreshing}
            >
              {refreshing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
          </CardTitle>
          <CardDescription>
            Email system performance and activity metrics
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {systemStatus.lastSent ? '✓' : '–'}
              </div>
              <div className="text-sm font-medium">Last Email Sent</div>
              <div className="text-xs text-muted-foreground">
                {systemStatus.lastSent 
                  ? new Date(systemStatus.lastSent).toLocaleString()
                  : 'Never'
                }
              </div>
            </div>
            
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className={`text-2xl font-bold ${systemStatus.recentFailures > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {systemStatus.recentFailures}
              </div>
              <div className="text-sm font-medium">Recent Failures</div>
              <div className="text-xs text-muted-foreground">
                Last 24 hours
              </div>
            </div>
            
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className={`text-2xl font-bold ${systemStatus.queuedNotifications > 0 ? 'text-blue-600' : 'text-muted-foreground'}`}>
                {systemStatus.queuedNotifications}
              </div>
              <div className="text-sm font-medium">Queued Notifications</div>
              <div className="text-xs text-muted-foreground">
                Pending delivery
              </div>
            </div>
          </div>
          
          <div className="text-sm text-muted-foreground">
            <p>
              Metrics are updated in real-time. Recent failures include emails that 
              failed to send in the last 24 hours. Queued notifications are emails 
              scheduled for delivery.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}