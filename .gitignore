# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# testing
/coverage
/.nyc_output

# security
/secrets
*.pem
*.key
*.crt

# logs
logs
*.log

# IDE
.vscode/settings.json
.idea/