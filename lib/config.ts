import { z } from 'zod'

// Environment validation schema
const envSchema = z.object({
  // Supabase Database
  NEXT_PUBLIC_SUPABASE_URL: z.string().url('Invalid Supabase URL'),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1, 'Supabase anon key is required'),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1, 'Supabase service role key is required'),
  
  // Authentication
  CLERK_SECRET_KEY: z.string().min(1, 'Clerk secret key is required'),
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: z.string().min(1, 'Clerk publishable key is required'),
  
  // Application
  NODE_ENV: z.enum(['development', 'staging', 'production']).default('development'),
  NEXT_PUBLIC_APP_URL: z.string().url('Invalid app URL').default('http://localhost:3000'),
  
  // Rate Limiting
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).pipe(z.number().positive()).default('100'),
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).pipe(z.number().positive()).default('60000'),
  
  // Logging
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
  ENABLE_CONSOLE_LOGGING: z.string().transform(val => val === 'true').default('true'),
  
  // Security
  CSRF_SECRET: z.string().min(32, 'CSRF secret must be at least 32 characters'),
  SESSION_SECRET: z.string().min(32, 'Session secret must be at least 32 characters'),
  
  // Optional external services
  SENTRY_DSN: z.string().url().optional(),
  ANALYTICS_ID: z.string().optional(),
})

// Parse and validate environment variables
function validateEnv() {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join('\n')
      throw new Error(`Environment validation failed:\n${missingVars}`)
    }
    throw error
  }
}

// Export validated environment configuration
export const env = validateEnv()

// Type-safe environment configuration
export type EnvConfig = z.infer<typeof envSchema>

// Helper functions
export const isDevelopment = env.NODE_ENV === 'development'
export const isProduction = env.NODE_ENV === 'production'
export const isStaging = env.NODE_ENV === 'staging'

// Supabase table names (no prefix needed as they're defined in schema)
export const TABLES = {
  departments: 'departments',
  employees: 'employees',
  managers: 'managers',
  userRoles: 'user_roles',
  employeeAssignments: 'employee_assignments',
  appraisalPeriods: 'appraisal_periods',
  appraisals: 'appraisals',
} as const

// Supabase configuration
export const supabaseConfig = {
  url: env.NEXT_PUBLIC_SUPABASE_URL,
  anonKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,
}

// Authentication configuration
export const authConfig = {
  clerkSecretKey: env.CLERK_SECRET_KEY,
  clerkPublishableKey: env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,
  sessionSecret: env.SESSION_SECRET,
}

// Rate limiting configuration
export const rateLimitConfig = {
  maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  windowMs: env.RATE_LIMIT_WINDOW_MS,
}

// Logging configuration
export const loggingConfig = {
  level: env.LOG_LEVEL,
  enableConsole: env.ENABLE_CONSOLE_LOGGING,
  sentryDsn: env.SENTRY_DSN,
}

// Security configuration
export const securityConfig = {
  csrfSecret: env.CSRF_SECRET,
  sessionSecret: env.SESSION_SECRET,
}

console.log(`🚀 Application starting in ${env.NODE_ENV} mode`)
console.log(`📊 Logging level: ${env.LOG_LEVEL}`)
console.log(`🔒 Security headers enabled: ${isProduction}`)
console.log(`🗄️ Supabase configured for: ${env.NEXT_PUBLIC_SUPABASE_URL}`)
