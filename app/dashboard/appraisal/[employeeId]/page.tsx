import { getAppraisalDetails, getEmployeeDetails } from "@/lib/data/index"
import { notFound } from "next/navigation"
import { AppraisalForm } from "@/components/appraisal-form"

export default async function AppraisalPage({ params }: { params: Promise<{ employeeId: string }> }) {
  const { employeeId } = await params
  const employee = await getEmployeeDetails(employeeId)
  const appraisal = await getAppraisalDetails(employeeId)

  if (!employee) {
    notFound()
  }

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Appraisal for {employee.fullName}</h1>
        <p className="text-muted-foreground">{employee.departmentName} - July 2025 Period</p>
      </div>
      <AppraisalForm employee={employee} appraisal={appraisal} />
    </div>
  )
}
