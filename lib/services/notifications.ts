import { supabaseAdmin } from '../supabase'
import { emailService, type NotificationData, type PendingAppraisal } from './email'
import { debug } from '../debug'

export interface NotificationPreferences {
  user_id: string
  email_enabled: boolean
  reminder_days_before: number
  reminder_time: string
}

export interface ScheduledNotification {
  id: string
  user_id: string
  notification_type: string
  scheduled_for: string
  period_id: string
  metadata: {
    period_end?: string
    error?: string
    [key: string]: unknown
  }
  processed: boolean
}

/**
 * Notification service for managing and processing notifications
 */
export class NotificationService {
  private static instance: NotificationService

  private constructor() {}

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService()
    }
    return NotificationService.instance
  }

  /**
   * Schedule monthly reminder notifications for all managers
   */
  async scheduleMonthlyReminders(): Promise<number> {
    try {
      debug.log('📅 [NOTIFICATIONS] Scheduling monthly reminders...')

      const { data, error } = await supabaseAdmin.rpc('schedule_monthly_reminders')

      if (error) {
        console.error('❌ [NOTIFICATIONS] Failed to schedule reminders:', error)
        throw error
      }

      debug.log(`✅ [NOTIFICATIONS] Scheduled ${data} reminder notifications`)
      return data || 0

    } catch (error) {
      console.error('🚨 [NOTIFICATIONS] Error scheduling monthly reminders:', error)
      throw error
    }
  }

  /**
   * Process all due scheduled notifications
   */
  async processScheduledNotifications(): Promise<number> {
    try {
      debug.log('⚡ [NOTIFICATIONS] Processing scheduled notifications...')

      // Get all due notifications
      const { data: notifications, error } = await supabaseAdmin
        .from('appy_scheduled_notifications')
        .select('*')
        .lte('scheduled_for', new Date().toISOString())
        .eq('processed', false)
        .eq('notification_type', 'appraisal_reminder')

      if (error) {
        console.error('❌ [NOTIFICATIONS] Failed to fetch scheduled notifications:', error)
        throw error
      }

      if (!notifications || notifications.length === 0) {
        debug.log('📭 [NOTIFICATIONS] No due notifications to process')
        return 0
      }

      let processedCount = 0

      for (const notification of notifications) {
        try {
          const success = await this.processAppraisalReminder(notification)
          if (success) {
            processedCount++
          }

          // Mark as processed regardless of email success
          await this.markNotificationProcessed(notification.id)

        } catch (error) {
          console.error(`🚨 [NOTIFICATIONS] Error processing notification ${notification.id}:`, error)
          await this.markNotificationProcessed(notification.id, error instanceof Error ? error.message : 'Unknown error')
        }
      }

      debug.log(`✅ [NOTIFICATIONS] Processed ${processedCount}/${notifications.length} notifications`)
      return processedCount

    } catch (error) {
      console.error('🚨 [NOTIFICATIONS] Error processing scheduled notifications:', error)
      throw error
    }
  }

  /**
   * Process a single appraisal reminder notification
   */
  private async processAppraisalReminder(notification: ScheduledNotification): Promise<boolean> {
    try {
      debug.log(`📧 [NOTIFICATIONS] Processing appraisal reminder for user: ${notification.user_id}`)

      // Get manager details
      const { data: manager, error: managerError } = await supabaseAdmin
        .from('appy_managers')
        .select('user_id, full_name, email')
        .eq('user_id', notification.user_id)
        .single()

      if (managerError || !manager) {
        console.error('❌ [NOTIFICATIONS] Manager not found:', notification.user_id)
        return false
      }

      // Get pending appraisals for this manager
      const { data: pendingAppraisals, error: appraisalsError } = await supabaseAdmin
        .rpc('get_pending_appraisals_for_manager', {
          manager_user_id: notification.user_id,
          period_id: notification.period_id
        })

      if (appraisalsError) {
        console.error('❌ [NOTIFICATIONS] Failed to get pending appraisals:', appraisalsError)
        return false
      }

      // Only send email if there are pending appraisals
      if (!pendingAppraisals || pendingAppraisals.length === 0) {
        debug.log(`📭 [NOTIFICATIONS] No pending appraisals for manager: ${notification.user_id}`)
        return true // Consider this successful since there's nothing to remind about
      }

      // Prepare notification data
      const notificationData: NotificationData = {
        user_id: notification.user_id,
        manager_name: manager.full_name,
        manager_email: manager.email,
        period_end: notification.metadata.period_end as string,
        pending_appraisals: pendingAppraisals.map((emp: any) => ({
          employee_id: emp.employee_id,
          employee_name: emp.employee_name,
          department_name: emp.department_name,
          days_remaining: emp.days_remaining
        }))
      }

      // Send email notification
      const emailSent = await emailService.sendAppraisalReminder(notificationData)

      if (emailSent) {
        debug.log(`✅ [NOTIFICATIONS] Appraisal reminder sent to: ${manager.email}`)
      } else {
        console.error(`❌ [NOTIFICATIONS] Failed to send appraisal reminder to: ${manager.email}`)
      }

      return emailSent

    } catch (error) {
      console.error('🚨 [NOTIFICATIONS] Error processing appraisal reminder:', error)
      return false
    }
  }

  /**
   * Mark notification as processed
   */
  private async markNotificationProcessed(notificationId: string, error?: string): Promise<void> {
    try {
      interface UpdateData {
        processed: boolean
        processed_at: string
        metadata?: {
          error: string
        }
      }

      const updateData: UpdateData = {
        processed: true,
        processed_at: new Date().toISOString()
      }

      if (error) {
        updateData.metadata = { error }
      }

      await supabaseAdmin
        .from('appy_scheduled_notifications')
        .update(updateData)
        .eq('id', notificationId)

    } catch (updateError) {
      console.error('Failed to mark notification as processed:', updateError)
    }
  }

  /**
   * Get notification preferences for a user
   */
  async getNotificationPreferences(userId: string): Promise<NotificationPreferences | null> {
    try {
      const { data, error } = await supabaseAdmin
        .from('appy_notification_preferences')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          // No preferences found, return defaults
          return {
            user_id: userId,
            email_enabled: true,
            reminder_days_before: 4,
            reminder_time: '09:00:00'
          }
        }
        throw error
      }

      return data
    } catch (error) {
      console.error('Error fetching notification preferences:', error)
      return null
    }
  }

  /**
   * Update notification preferences for a user
   */
  async updateNotificationPreferences(userId: string, preferences: Partial<NotificationPreferences>): Promise<boolean> {
    try {
      const { error } = await supabaseAdmin
        .from('appy_notification_preferences')
        .upsert({
          user_id: userId,
          ...preferences,
          updated_at: new Date().toISOString()
        })

      if (error) {
        console.error('Error updating notification preferences:', error)
        return false
      }

      debug.log(`✅ [NOTIFICATIONS] Updated preferences for user: ${userId}`)
      return true

    } catch (error) {
      console.error('Error updating notification preferences:', error)
      return false
    }
  }

  /**
   * Get notification history for a user
   */
  async getNotificationHistory(userId: string, limit: number = 50): Promise<any[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('appy_notification_log')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('Error fetching notification history:', error)
        return []
      }

      return data || []

    } catch (error) {
      console.error('Error fetching notification history:', error)
      return []
    }
  }

  /**
   * Send immediate notification (for testing or urgent notifications)
   */
  async sendImmediateAppraisalReminder(managerId: string, periodId: string): Promise<boolean> {
    try {
      debug.log(`🚀 [NOTIFICATIONS] Sending immediate reminder to manager: ${managerId}`)

      // Get the actual period end date from the database
      const { data: period, error: periodError } = await supabaseAdmin
        .from('appy_appraisal_periods')
        .select('end_date')
        .eq('id', periodId)
        .single()

      if (periodError || !period) {
        console.error('❌ [NOTIFICATIONS] Failed to get period end date:', periodError)
        return false
      }

      // Create a temporary notification object with actual period end date
      const notification: ScheduledNotification = {
        id: 'immediate',
        user_id: managerId,
        notification_type: 'appraisal_reminder',
        scheduled_for: new Date().toISOString(),
        period_id: periodId,
        metadata: {
          period_end: period.end_date
        },
        processed: false
      }

      return await this.processAppraisalReminder(notification)

    } catch (error) {
      console.error('Error sending immediate notification:', error)
      return false
    }
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance()

// Console log for debugging
debug.log('🔔 Notification service initialized')
