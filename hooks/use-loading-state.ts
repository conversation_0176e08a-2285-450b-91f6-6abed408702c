'use client'

import { useState, useCallback, useRef, useEffect } from 'react'

interface LoadingState {
  isLoading: boolean
  error: string | null
  success: boolean
}

interface UseLoadingStateOptions {
  initialLoading?: boolean
  successDuration?: number
  errorDuration?: number
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function useLoadingState(options: UseLoadingStateOptions = {}) {
  const {
    initialLoading = false,
    successDuration = 3000,
    errorDuration = 5000,
    onSuccess,
    onError,
  } = options

  const [state, setState] = useState<LoadingState>({
    isLoading: initialLoading,
    error: null,
    success: false,
  })

  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Clear timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading,
      error: loading ? null : prev.error, // Clear error when starting new loading
      success: loading ? false : prev.success, // Clear success when starting new loading
    }))
  }, [])

  const setError = useCallback((error: string | null) => {
    setState(prev => ({
      ...prev,
      isLoading: false,
      error,
      success: false,
    }))

    if (error) {
      onError?.(error)
      
      // Auto-clear error after specified duration
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      timeoutRef.current = setTimeout(() => {
        setState(prev => ({ ...prev, error: null }))
      }, errorDuration)
    }
  }, [errorDuration, onError])

  const setSuccess = useCallback((success: boolean = true) => {
    setState(prev => ({
      ...prev,
      isLoading: false,
      error: null,
      success,
    }))

    if (success) {
      onSuccess?.()
      
      // Auto-clear success after specified duration
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      timeoutRef.current = setTimeout(() => {
        setState(prev => ({ ...prev, success: false }))
      }, successDuration)
    }
  }, [successDuration, onSuccess])

  const reset = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setState({
      isLoading: false,
      error: null,
      success: false,
    })
  }, [])

  const execute = useCallback(async <T>(
    asyncFunction: () => Promise<T>,
    options?: {
      successMessage?: string
      errorMessage?: string
      onSuccess?: (result: T) => void
      onError?: (error: Error) => void
    }
  ): Promise<T | null> => {
    try {
      setLoading(true)
      const result = await asyncFunction()
      setSuccess(true)
      options?.onSuccess?.(result)
      return result
    } catch (error) {
      const errorMessage = options?.errorMessage || 
        (error instanceof Error ? error.message : 'An unexpected error occurred')
      setError(errorMessage)
      options?.onError?.(error instanceof Error ? error : new Error(String(error)))
      return null
    }
  }, [setLoading, setSuccess, setError])

  return {
    ...state,
    setLoading,
    setError,
    setSuccess,
    reset,
    execute,
    // Computed states
    hasError: !!state.error,
    isIdle: !state.isLoading && !state.error && !state.success,
  }
}

// Hook for managing multiple loading states
export function useMultipleLoadingStates<T extends string>(keys: T[]) {
  const [states, setStates] = useState<Record<T, LoadingState>>(() =>
    keys.reduce((acc, key) => ({
      ...acc,
      [key]: { isLoading: false, error: null, success: false }
    }), {} as Record<T, LoadingState>)
  )

  const setLoading = useCallback((key: T, loading: boolean) => {
    setStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        isLoading: loading,
        error: loading ? null : prev[key].error,
        success: loading ? false : prev[key].success,
      }
    }))
  }, [])

  const setError = useCallback((key: T, error: string | null) => {
    setStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        isLoading: false,
        error,
        success: false,
      }
    }))
  }, [])

  const setSuccess = useCallback((key: T, success: boolean = true) => {
    setStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        isLoading: false,
        error: null,
        success,
      }
    }))
  }, [])

  const reset = useCallback((key?: T) => {
    if (key) {
      setStates(prev => ({
        ...prev,
        [key]: { isLoading: false, error: null, success: false }
      }))
    } else {
      setStates(prev =>
        keys.reduce((acc, k) => ({
          ...acc,
          [k]: { isLoading: false, error: null, success: false }
        }), {} as Record<T, LoadingState>)
      )
    }
  }, [keys])

  const isAnyLoading = (Object.values(states) as LoadingState[]).some(state => state.isLoading)
  const hasAnyError = (Object.values(states) as LoadingState[]).some(state => !!state.error)
  const hasAnySuccess = (Object.values(states) as LoadingState[]).some(state => state.success)

  return {
    states,
    setLoading,
    setError,
    setSuccess,
    reset,
    isAnyLoading,
    hasAnyError,
    hasAnySuccess,
    getState: (key: T) => states[key],
  }
}

// Hook for form-specific loading states
export function useFormLoadingState() {
  const loadingState = useLoadingState({
    successDuration: 2000,
    errorDuration: 5000,
  })

  const submitForm = useCallback(async <T>(
    submitFunction: () => Promise<T>,
    options?: {
      onSuccess?: (result: T) => void
      onError?: (error: Error) => void
      resetOnSuccess?: boolean
    }
  ) => {
    const result = await loadingState.execute(submitFunction, {
      onSuccess: (result) => {
        options?.onSuccess?.(result)
        if (options?.resetOnSuccess) {
          setTimeout(() => loadingState.reset(), 2000)
        }
      },
      onError: options?.onError,
    })

    return result
  }, [loadingState])

  return {
    ...loadingState,
    submitForm,
    isSubmitting: loadingState.isLoading,
    submitError: loadingState.error,
    submitSuccess: loadingState.success,
  }
}

console.log('⏳ Loading state management hooks loaded')
