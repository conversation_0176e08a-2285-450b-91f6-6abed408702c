'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Users, 
  FileText, 
  CheckCircle, 
  Clock, 
  Building, 
  UserCheck,
  TrendingUp,
  Shield,
  Settings,
  Download
} from 'lucide-react'
import { AdminProfile, AdminPermissions } from '@/lib/admin-data'
import { AuthUser } from '@/lib/auth'
import { AdminStatsCards } from './admin-stats-cards'
import { AdminManagement } from './admin-management'
import { AdminReports } from './admin-reports'
import { AdminSettings } from './admin-settings'

interface Props {
  admin: AdminProfile
  permissions: AdminPermissions
  currentUser: AuthUser
  stats?: any
  allAdmins?: AdminProfile[]
}

export function AdminDashboard({ admin, permissions, currentUser, stats, allAdmins = [] }: Props) {
  const isSuperAdmin = currentUser?.role === 'super-admin'
  const isOwner = currentUser?.id === admin.id
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {admin.fullName}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge 
            variant={admin.role === 'super-admin' ? 'default' : 'secondary'}
            className="flex items-center gap-1"
          >
            <Shield className="h-3 w-3" />
            {admin.role === 'super-admin' ? 'Super Admin' : 'Admin'}
          </Badge>
          {admin.supervisorName && (
            <Badge variant="outline" className="text-xs">
              Reports to: {admin.supervisorName}
            </Badge>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <AdminStatsCards stats={stats} />

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          {isSuperAdmin && (
            <TabsTrigger value="management">Admin Management</TabsTrigger>
          )}
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Overview</CardTitle>
              <CardDescription>
                Welcome to the admin dashboard. Use the tabs above to manage different aspects of the system.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <h4 className="font-medium">Quick Actions</h4>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <Users className="h-4 w-4 mr-2" />
                      View All Employees
                    </Button>
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <FileText className="h-4 w-4 mr-2" />
                      Generate Reports
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">System Status</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span className="text-sm">Database: Connected</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span className="text-sm">Email System: Active</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports">
          <AdminReports permissions={permissions} />
        </TabsContent>

        {isSuperAdmin && (
          <TabsContent value="management">
            <AdminManagement currentUser={currentUser} admins={allAdmins} />
          </TabsContent>
        )}

        <TabsContent value="settings">
          <AdminSettings admin={admin} permissions={permissions} />
        </TabsContent>
      </Tabs>
    </div>
  )
}