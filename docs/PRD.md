Product Requirements Document  
Project: Monthly Appraisal & Payroll Approval Tool  
Date: 7 Jul 2025  

1. Vision & Objectives  
- Replace the current screenshot/email workflow with a single web application that:  
  - Lets every manager submit a short monthly appraisal for each direct report  
this should be standard questions not text ill attach the questionnaire in the comment
  - Gives Accounting a real-time, color-coded view of who is cleared for payment  
  - Gives HR a single source of truth for the org chart (contractors, managers, departments)  
  - Ships an internal MVP by Thursday (4-day window)
track amount of days off 
I would like the contractors to also be able to send comments each month about any initiatives they took this month, especially on the ai side and fill any complaint they might have or request, it should land on their profile for hr to check

2. Success Metrics (MVP)  
- \( \ge 95\% \) of managers submit all appraisals within the first release cycle  
- Accounting spends \( \le 15 \) min/month importing approvals (today: \( \approx 4 \) h)  
- Zero payment errors attributable to wrong hours/rates during the first 2 months

3. Roles  
- <PERSON> – Sponsor / Final approver  
-<PERSON> - admin access 
- HR (<PERSON><PERSON>) – Data owner for contractors, managers, departments  
- Accounting – Consumes approval list, triggers payment  
- Managers – Fill monthly appraisal forms  
- <PERSON> – <PERSON> (lead), <PERSON><PERSON> (support)  
- contractors (Phase 2) – Optional self-reporting

4. User Types & Permissions  
- Super-Admin: full access to everything (<PERSON>, <PERSON>, Engineering, Joelle)  
- HR-Admin: CRUD contractors, managers, departments  
- Manager: read assigned contractors, create/update own appraisals within the period  only the appraisals they fill or their subordinates fill, its important they don't see the ones filled by their managers or other
- Accountant: read-only access to approved appraisals, export CSV  
- contractors (Phase 2): read own history, optionally submit self-reports

5. High-Level Feature List  
5.1 Manager Portal  
- Dashboard lists all direct reports + submission status (Not Started / Draft / Submitted) this is why we need the forn to be as quantifiable as possible, which is what I tried to do below but let me know if you want to change anything
- Five-question appraisal form (radio / checkbox / text)  
- Auto-prefill with previous month’s answers, editable until deadline  Love this 
- Autosave drafts  
- Submit / edit cut-off: 23:59 on the 29th (configurable)

5.2 Accounting Dashboard  
- Table view of all submitted appraisals for the active pay period  
- Color legend: green = Submitted, grey = Missing, yellow = Draft  
- CSV export of green rows (contractors, Hours, Pay Rate (hr needs to keep this updated please note that some are per hour and some are monthly retainers, for hourly not sure if we should link it to hubstaff so that they are automatically filled), 
Field: Compensation Type (Hourly / Monthly)
Field: Total Hours Worked (Only if Hourly)
Auto-hide Total Hours if Compensation = Monthly (for clean CSV export)
Manager, Timestamp)  
- Mark “Paid” (optional, Phase 2)


5.3 HR Admin Panel  
- CRUD contractors (name, department, compensation type, hourly/monthly rate)  
- Assign one primary manager (multi-manager support in Phase 2)  
- Upload bulk CSV template  
- Close / open new appraisal periods
Edit compensation type (Hourly / Monthly)
Edit pay rate
Edit currency (optional, if contractors are international)

5.4 System Notifications (email via Clerk)  
- Opening-period reminder to managers  
- 24 h before deadline reminder for missing submissions  
- Confirmations to Accounting when all green

5.5 Audit Log  
- Row-level history of create/update/delete events (Supabase RLS + pgAudit)

5.6 Phase 2 Backlog (post-MVP)  
- contractors self-report section  
- Multiple managers per contractors  
- KPI-specific questions per department  
- Slack / WhatsApp notifications  
- Role-based analytics
Contractor self-reporting interface (AI initiatives, complaints, requests)
HR inbox view of contractor self-submissions per profile
Manager hierarchy: allow >1 manager per contractor
Integration: Hour-tracking from Hubstaff (for hourly roles)

6. Functional Requirements (MVP)  
6.1 Authentication & Authorization  
- Clerk handles sign-up & SSO (email magic link)  
- Role stored in Supabase `user_roles` table  
- Supabase Row Level Security enforces per-role access

6.2 Data Model (PostgreSQL on Supabase)

```sql
-- prettier-ignore
CREATE TABLE departments (
  id              uuid primary key default gen_random_uuid(),
  name            text not null unique
);

CREATE TABLE contractors (
  id              uuid primary key default gen_random_uuid(),
  full_name       text not null,
  compensation    text not null check (compensation in ('hourly','monthly')),
  rate            numeric(10,2) not null,
  department_id   uuid references departments(id),
  active          boolean default true
);

CREATE TABLE managers (
  user_id         uuid primary key,                   -- references Clerk id
  full_name       text not null
);

CREATE TABLE contractors_assignments (
  contractors_id     uuid references contractors(id),
  manager_id      uuid references managers(user_id),
  primary key (contractors_id, manager_id)
);

CREATE TYPE appraisal_status AS ENUM ('draft','submitted');

CREATE TABLE appraisal_periods (
  id              uuid primary key default gen_random_uuid(),
  period_start    date not null,
  period_end      date not null,
  closed          boolean default false
);

CREATE TABLE appraisals (
  id              uuid primary key default gen_random_uuid(),
  period_id       uuid references appraisal_periods(id),
  contractors_id     uuid references contractors(id),
  manager_id      uuid references managers(user_id),
  q1              text,
  q2              text,
  q3              text,
  q4              text,
  q5              text,
  status          appraisal_status default 'draft',
  submitted_at    timestamptz
);
```

6.3 API Routes (Next.js / Route Handlers)  
- `GET /api/periods/current` – active period meta  
- `GET /api/appraisals?period={id}` – manager’s list  
- `POST /api/appraisals` – create/update draft  
- `POST /api/appraisals/submit` – finalize  
- `GET /api/accounting/export?period={id}` – CSV (accounting only)  
- `POST /api/admin/contractors` – CRUD contractors (HR admin)

7. Non-Functional Requirements  
- Performance: initial dashboard TTFB \< 300 ms for 1 k contractors  
- Availability: \( 99.5\% \) (Supabase + Vercel edge)  
- Security: RLS everywhere, TLS 1.3, OWASP top-10 mitigations  
- Compliance: export audit logs on request (ISO 27001 internal)  
- Accessibility: WCAG 2.1 AA

8. Tech Stack (confirmed)  
- Frontend & API Routes: Next.js 14 / App Router  
- Database: PostgreSQL (Supabase)  
- Auth: Clerk  
- Runtime & Package Manager: Bun  
- Monorepo: Turborepo (apps/web, packages/ui, packages/db)  
- Styling: Tailwind CSS (internal convention)

End of PRD