import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { getPendingFeedbackForHR, getFeedbackStatistics } from '@/lib/data/feedback'

/**
 * GET - Get pending feedback and statistics for HR dashboard
 */
export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has HR permissions
    if (!['hr-admin', 'super-admin'].includes(currentUser.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get query parameters for date filtering
    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // Fetch pending feedback and statistics
    const [pendingFeedback, statistics] = await Promise.all([
      getPendingFeedbackForHR(),
      getFeedbackStatistics(startDate || undefined, endDate || undefined)
    ])

    return NextResponse.json({ 
      success: true, 
      data: {
        pendingFeedback,
        statistics
      }
    })

  } catch (error) {
    console.error('Error fetching HR feedback data:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch feedback data' 
    }, { status: 500 })
  }
}
