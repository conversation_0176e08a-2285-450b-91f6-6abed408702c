"use client"

import { Employee } from "@/lib/types"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  DollarSign,
  Eye,
  EyeOff,
  Shield,
  Clock,
  Calendar
} from "lucide-react"
import { useState } from "react"

interface CompensationSectionProps {
  employee: Employee
  canViewRates: boolean
}

export function CompensationSection({ employee, canViewRates }: CompensationSectionProps) {
  const [showRate, setShowRate] = useState(false)

  if (!canViewRates) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-orange-100 dark:bg-orange-900">
              <Shield className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <CardTitle className="text-lg">Compensation</CardTitle>
              <p className="text-sm text-muted-foreground">
                Access restricted
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <div className="mx-auto mb-4 p-3 rounded-full bg-muted w-fit">
              <Shield className="h-8 w-8 text-muted-foreground" />
            </div>
            <p className="text-muted-foreground">
              You don't have permission to view compensation details.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="transition-all duration-300 hover:shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-green-100 dark:bg-green-900">
              <DollarSign className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <CardTitle className="text-lg">Compensation</CardTitle>
              <p className="text-sm text-muted-foreground">
                Employment and salary details
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowRate(!showRate)}
            className="gap-2 transition-all duration-200 hover:bg-muted/70"
          >
            {showRate ? (
              <>
                <EyeOff className="h-4 w-4" />
                Hide
              </>
            ) : (
              <>
                <Eye className="h-4 w-4" />
                Show
              </>
            )}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid gap-6">
          {/* Employment Type */}
          <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg transition-all duration-200 hover:bg-muted/70">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900">
                {employee.compensation === 'hourly' ? (
                  <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                ) : (
                  <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                )}
              </div>
              <div>
                <p className="font-medium">Employment Type</p>
                <p className="text-sm text-muted-foreground">
                  {employee.compensation === 'hourly' ? 'Hourly Employee' : 'Monthly Employee'}
                </p>
              </div>
            </div>
            <Badge variant="secondary" className="capitalize">
              {employee.compensation}
            </Badge>
          </div>

          {/* Compensation Rate */}
          {showRate && (
            <div className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950 rounded-lg border-2 border-green-200 dark:border-green-800 animate-in slide-in-from-top-2 duration-300">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-700 dark:text-green-300 mb-2">
                  ${employee.rate.toLocaleString()}
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                    per {employee.compensation === 'hourly' ? 'hour' : 'month'}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    Current Rate
                  </Badge>
                </div>
              </div>
              
              {/* Additional calculations */}
              <div className="grid grid-cols-2 gap-4 mt-6 pt-4 border-t border-green-200 dark:border-green-800">
                <div className="text-center">
                  <p className="text-lg font-semibold text-green-700 dark:text-green-300">
                    {employee.compensation === 'hourly' 
                      ? `$${(employee.rate * 40 * 4).toLocaleString()}`
                      : `$${(employee.rate * 12).toLocaleString()}`
                    }
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {employee.compensation === 'hourly' ? 'Monthly (40h/week)' : 'Annual'}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-semibold text-green-700 dark:text-green-300">
                    {employee.compensation === 'hourly' 
                      ? `$${(employee.rate * 40 * 52).toLocaleString()}`
                      : `$${employee.rate.toLocaleString()}`
                    }
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {employee.compensation === 'hourly' ? 'Annual (40h/week)' : 'Monthly'}
                  </p>
                </div>
              </div>
            </div>
          )}

          {!showRate && (
            <div className="text-center py-8 text-muted-foreground">
              <DollarSign className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>Click "Show" to view compensation details</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}