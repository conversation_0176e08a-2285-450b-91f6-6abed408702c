import { requirePermission } from "@/lib/auth"
import { getPendingApprovals } from "@/lib/data/index"
import { AppraisalApprovalsTable } from "@/components/appraisal-approvals-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>he<PERSON>, Clock, CheckCircle2 } from "lucide-react"

export default async function ApprovalsPage() {
  // Require approval permissions to access this page
  await requirePermission('appraisal:approve')
  
  // Get pending appraisals that need approval
  const pendingAppraisals = await getPendingApprovals()
  
  const stats = {
    pending: pendingAppraisals.filter(a => a.status === 'submitted').length,
    total: pendingAppraisals.length
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Appraisal Approvals</h1>
        <p className="text-muted-foreground">
          Review and approve submitted employee appraisals
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">
              Waiting for your review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Submissions</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              This period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Your Role</CardTitle>
            <ShieldCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Approver</div>
            <p className="text-xs text-muted-foreground">
              Super Admin / HR Admin
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Approvals Table */}
      <AppraisalApprovalsTable
        appraisals={pendingAppraisals}
        title="Pending Appraisal Approvals"
        description="Review and approve submitted employee performance appraisals"
        emptyMessage="No appraisals are currently pending approval."
      />
    </div>
  )
}
