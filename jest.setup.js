import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// Mock Next.js headers
jest.mock('next/headers', () => ({
  cookies() {
    return {
      get: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
    }
  },
  headers() {
    return new Map()
  },
}))

// Mock Clerk authentication
jest.mock('@clerk/nextjs', () => ({
  auth: jest.fn(() => ({
    userId: 'test-user-id',
    sessionId: 'test-session-id',
  })),
  currentUser: jest.fn(() => ({
    id: 'test-user-id',
    firstName: 'Test',
    lastName: 'User',
    emailAddresses: [{ emailAddress: '<EMAIL>' }],
  })),
  ClerkProvider: ({ children }) => children,
  SignInButton: ({ children }) => children,
  SignUpButton: ({ children }) => children,
  UserButton: () => <div data-testid="user-button">User Button</div>,
}))

// Mock our auth functions
jest.mock('@/lib/auth', () => ({
  getCurrentUser: jest.fn(() => Promise.resolve({
    id: 'test-user-id',
    fullName: 'Test User',
    role: 'manager',
  })),
  requireAuth: jest.fn(() => Promise.resolve({
    id: 'test-user-id',
    fullName: 'Test User',
    role: 'manager',
  })),
}))

// Mock data functions
jest.mock('@/lib/data', () => ({
  getEmployees: jest.fn(() => Promise.resolve([])),
  getDepartments: jest.fn(() => Promise.resolve([])),
  getManagers: jest.fn(() => Promise.resolve([])),
  getAppraisalPeriods: jest.fn(() => Promise.resolve([])),
  getAccountingData: jest.fn(() => Promise.resolve([])),
}))

// Mock server actions
jest.mock('@/lib/actions', () => ({
  saveAppraisalDraftAction: jest.fn(),
  submitAppraisalAction: jest.fn(),
  saveDepartmentAction: jest.fn(),
  saveEmployeeAction: jest.fn(),
  savePeriodAction: jest.fn(),
}))

// Mock accessibility functions
jest.mock('@/lib/accessibility', () => ({
  announceToScreenReader: jest.fn(),
  announceFormError: jest.fn(),
  announceFormSuccess: jest.fn(),
  announceLoadingState: jest.fn(),
  announceError: jest.fn(),
  getStatusAnnouncement: jest.fn((status) => `Status: ${status}`),
  getTableAnnouncement: jest.fn((rows, cols) => `Table with ${rows} rows and ${cols} columns`),
  getSortAnnouncement: jest.fn((column, direction) => `${column} sorted ${direction || 'none'}`),
  generateId: jest.fn(() => 'test-id'),
  trapFocus: jest.fn(),
  handleArrowNavigation: jest.fn(),
  createSkipLink: jest.fn(),
}))

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn(),
  },
}))

// Global test utilities
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
})

// Console logging for accessibility tests
const originalConsoleLog = console.log
console.log = (...args) => {
  // Only show accessibility logs in tests if needed
  if (args[0] && typeof args[0] === 'string' && args[0].includes('[A11Y]')) {
    // Optionally suppress or modify accessibility logs in tests
    return
  }
  originalConsoleLog(...args)
}

// Setup for accessibility testing
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks()
  
  // Reset DOM
  document.body.innerHTML = ''
  
  // Reset any global state
  if (typeof window !== 'undefined') {
    window.history.replaceState({}, '', '/')
  }
})

afterEach(() => {
  // Cleanup after each test
  jest.clearAllTimers()
})
