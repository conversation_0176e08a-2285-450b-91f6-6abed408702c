import { NextRequest, NextResponse } from 'next/server'
import { notificationService } from '@/lib/services/notifications'
import { debug } from '@/lib/debug'

/**
 * Cron job endpoint for processing scheduled notifications
 * This should be called daily by a cron service (Vercel Cron, GitHub Actions, etc.)
 * 
 * Example cron schedule: "0 9 * * *" (daily at 9 AM)
 */
export async function GET(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (cron service)
    const authHeader = request.headers.get('authorization')
    const adminSecret = process.env.ADMIN_SECRET

    if (!adminSecret || authHeader !== `Bearer ${adminSecret}`) {
      console.error('❌ [CRON] Unauthorized cron request - missing or invalid secret')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    debug.log('🕐 [CRON] Starting notification processing job...')

    // Schedule monthly reminders (this will only create new ones if needed)
    const scheduledCount = await notificationService.scheduleMonthlyReminders()
    debug.log(`📅 [CRON] Scheduled ${scheduledCount} new reminder notifications`)

    // Process due notifications
    const processedCount = await notificationService.processScheduledNotifications()
    debug.log(`⚡ [CRON] Processed ${processedCount} notifications`)

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      scheduled: scheduledCount,
      processed: processedCount,
      message: `Successfully scheduled ${scheduledCount} and processed ${processedCount} notifications`
    }

    debug.log('✅ [CRON] Notification processing job completed:', response)

    return NextResponse.json(response)

  } catch (error) {
    console.error('🚨 [CRON] Error in notification processing job:', error)

    const errorResponse = {
      success: false,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to process notifications'
    }

    return NextResponse.json(errorResponse, { status: 500 })
  }
}

/**
 * POST endpoint for manual notification processing (for testing)
 */
export async function POST(request: NextRequest) {
  try {
    // Require authentication for manual triggers
    const authHeader = request.headers.get('authorization')
    const adminSecret = process.env.ADMIN_SECRET

    if (!adminSecret || authHeader !== `Bearer ${adminSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, managerId, periodId } = body

    // Validate action parameter
    if (!action || typeof action !== 'string') {
      return NextResponse.json({ error: 'Action parameter is required and must be a string' }, { status: 400 })
    }

    const validActions = ['schedule_reminders', 'process_notifications', 'send_immediate']
    if (!validActions.includes(action)) {
      return NextResponse.json({ error: `Invalid action. Must be one of: ${validActions.join(', ')}` }, { status: 400 })
    }

    debug.log('🔧 [MANUAL] Manual notification trigger:', { action, managerId, periodId })

    let result: any = { success: false }

    try {
      switch (action) {
        case 'schedule_reminders':
          const scheduledCount = await notificationService.scheduleMonthlyReminders()
          result = {
            success: true,
            action: 'schedule_reminders',
            scheduled: scheduledCount,
            message: `Scheduled ${scheduledCount} reminder notifications`
          }
          break

        case 'process_notifications':
          const processedCount = await notificationService.processScheduledNotifications()
          result = {
            success: true,
            action: 'process_notifications',
            processed: processedCount,
            message: `Processed ${processedCount} notifications`
          }
          break

        case 'send_immediate':
          // Validate required parameters
          if (!managerId || typeof managerId !== 'string') {
            return NextResponse.json({ error: 'managerId is required and must be a string' }, { status: 400 })
          }
          if (!periodId || typeof periodId !== 'string') {
            return NextResponse.json({ error: 'periodId is required and must be a string' }, { status: 400 })
          }
          
          const sent = await notificationService.sendImmediateAppraisalReminder(managerId, periodId)
          result = {
            success: sent,
            action: 'send_immediate',
            managerId,
            periodId,
            message: sent ? 'Immediate notification sent' : 'Failed to send immediate notification'
          }
          break

        default:
          return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
      }
    } catch (error) {
      debug.error(`❌ [MANUAL] Error processing ${action}:`, error)
      return NextResponse.json({
        success: false,
        action,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: `Failed to execute ${action}`
      }, { status: 500 })
    }

    debug.log('✅ [MANUAL] Manual notification action completed:', result)
    return NextResponse.json(result)

  } catch (error) {
    console.error('🚨 [MANUAL] Error in manual notification trigger:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to execute manual notification action'
    }, { status: 500 })
  }
}
