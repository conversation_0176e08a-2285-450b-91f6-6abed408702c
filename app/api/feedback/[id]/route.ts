import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { db } from '@/lib/db'
import { z } from 'zod'

const updateStatusSchema = z.object({
  status: z.enum(['pending', 'under_review', 'investigating', 'resolved', 'closed', 'escalated']).optional(),
  hrResponse: z.string().optional(),
  hrNotes: z.string().optional(),
  resolutionSummary: z.string().optional(),
  requiresFollowup: z.boolean().optional(),
  followupDate: z.string().optional(),
  followupNotes: z.string().optional()
})

const addCommentSchema = z.object({
  comment: z.string().min(1).max(1000),
  isInternal: z.boolean().default(true)
})

/**
 * GET - Get feedback details by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    console.log('🔄 Fetching feedback details for ID:', id)

    const { userId } = await auth()
    if (!userId) {
      console.log('❌ Unauthorized feedback details request')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has HR or admin permissions
    const userRole = await db.getUserRole(userId)
    if (!userRole || !['hr-admin', 'super-admin'].includes(userRole)) {
      console.log('❌ Insufficient permissions for feedback details:', userRole)
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Fetch feedback details
    const feedback = await db.getFeedbackById(id)
    
    // Check if feedback exists
    if (!feedback) {
      console.log('❌ Feedback not found for ID:', id)
      return NextResponse.json({ 
        error: 'Feedback not found' 
      }, { status: 404 })
    }

    // Fetch comments
    const comments = await db.getFeedbackComments(id)

    console.log('✅ Feedback details fetched successfully')

    return NextResponse.json({
      success: true,
      feedback,
      comments
    })

  } catch (error) {
    console.error('❌ Error fetching feedback details:', error)
    return NextResponse.json({
      error: 'Failed to fetch feedback details'
    }, { status: 500 })
  }
}

/**
 * PATCH - Update feedback status
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    console.log('🔄 Updating feedback status for ID:', id)

    const { userId } = await auth()
    if (!userId) {
      console.log('❌ Unauthorized feedback update request')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has HR or admin permissions
    const userRole = await db.getUserRole(userId)
    if (!userRole || !['hr-admin', 'super-admin'].includes(userRole)) {
      console.log('❌ Insufficient permissions for feedback update:', userRole)
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = updateStatusSchema.parse(body)

    console.log('📝 Updating feedback with data:', validatedData)

    // Get manager record for the current user
    const manager = await db.getManagerByClerkId(userId)
    if (!manager) {
      console.log('❌ Manager not found for user:', userId)
      return NextResponse.json({
        error: 'Manager record not found'
      }, { status: 404 })
    }

    // Update feedback - explicitly specify fields
    const updatedFeedback = await db.updateFeedbackStatus(id, {
      status: validatedData.status,
      hrResponse: validatedData.hrResponse,
      hrNotes: validatedData.hrNotes,
      resolutionSummary: validatedData.resolutionSummary,
      requiresFollowup: validatedData.requiresFollowup,
      followupDate: validatedData.followupDate,
      followupNotes: validatedData.followupNotes,
      reviewedBy: manager.user_id,
      resolvedBy: validatedData.status === 'resolved' ? manager.user_id : undefined
    })

    console.log('✅ Feedback updated successfully')

    return NextResponse.json({
      success: true,
      feedback: updatedFeedback,
      message: 'Feedback updated successfully'
    })

  } catch (error) {
    console.error('❌ Error updating feedback:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Invalid input data',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      error: 'Failed to update feedback'
    }, { status: 500 })
  }
}

/**
 * POST - Add comment to feedback
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    console.log('🔄 Adding comment to feedback ID:', id)

    const { userId } = await auth()
    if (!userId) {
      console.log('❌ Unauthorized feedback comment request')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has HR or admin permissions
    const userRole = await db.getUserRole(userId)
    if (!userRole || !['hr-admin', 'super-admin'].includes(userRole)) {
      console.log('❌ Insufficient permissions for feedback comment:', userRole)
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = addCommentSchema.parse(body)

    console.log('💬 Adding comment to feedback ID:', id, '- Comment length:', validatedData.comment.length)

    // Get manager record for the current user
    const manager = await db.getManagerByClerkId(userId)
    if (!manager) {
      console.log('❌ Manager not found for user:', userId)
      return NextResponse.json({
        error: 'Manager record not found'
      }, { status: 404 })
    }

    // Add comment
    const comment = await db.addFeedbackComment({
      feedbackId: id,
      commenterId: manager.user_id,
      comment: validatedData.comment,
      isInternal: validatedData.isInternal
    })

    console.log('✅ Comment added successfully')

    return NextResponse.json({
      success: true,
      comment,
      message: 'Comment added successfully'
    })

  } catch (error) {
    console.error('❌ Error adding comment:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Invalid input data',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      error: 'Failed to add comment'
    }, { status: 500 })
  }
}
