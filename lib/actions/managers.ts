'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { saveManager } from '../data'
import { managerFormSchema } from '../schemas'
import { ZodError } from 'zod'

export async function saveManagerAction(formData: FormData) {
  try {
    const data = {
      fullName: formData.get('fullName') as string,
      email: formData.get('email') as string,
      role: formData.get('role') as string,
      departmentId: formData.get('departmentId') as string || undefined,
    }

    // Validate the data
    const validatedData = managerFormSchema.parse(data)

    // Save the manager
    const newManager = await saveManager(validatedData)

    // Revalidate relevant pages
    revalidatePath('/dashboard')
    revalidatePath('/dashboard/add-people')
    revalidatePath('/dashboard/managers')

    return { 
      success: true, 
      data: newManager,
      message: 'Manager created successfully' 
    }
  } catch (error) {
    console.error('Failed to save manager:', error)
    
    if (error instanceof ZodError) {
      return { 
        success: false, 
        error: 'Validation failed',
        details: error.errors 
      }
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to create manager' 
    }
  }
}

export async function createManagerAndRedirectAction(formData: FormData) {
  const result = await saveManagerAction(formData)
  
  if (result.success) {
    redirect('/dashboard/add-people?success=manager-created')
  }
  
  return result
}