"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { EmployeeKPI } from "@/lib/types"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import { createEmployeeKPI, updateEmployeeKPI } from "@/lib/actions/employee-kpis"
import { ArrowLeft, Loader2 } from "lucide-react"

const kpiFormSchema = z.object({
  kpiName: z.string().min(1, "KPI name is required").max(255),
  kpiValue: z.string().optional(),
  kpiTarget: z.string().optional(),
  kpiUnit: z.string().optional(),
  period: z.string().optional(),
  description: z.string().max(500).optional(),
})

type KPIFormValues = z.infer<typeof kpiFormSchema>

interface EmployeeKPIFormProps {
  employeeId: string
  kpi?: EmployeeKPI | null
  onClose: () => void
}

const commonPeriods = [
  "Q1 2024",
  "Q2 2024",
  "Q3 2024",
  "Q4 2024",
  "Monthly",
  "Quarterly",
  "Yearly",
  "Current Period",
]

const commonUnits = [
  "%",
  "$",
  "hours",
  "days",
  "points",
  "count",
  "score",
  "rating",
]

export function EmployeeKPIForm({ employeeId, kpi, onClose }: EmployeeKPIFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = !!kpi

  const form = useForm<KPIFormValues>({
    resolver: zodResolver(kpiFormSchema),
    defaultValues: {
      kpiName: kpi?.kpiName || "",
      kpiValue: kpi?.kpiValue || "",
      kpiTarget: kpi?.kpiTarget || "",
      kpiUnit: kpi?.kpiUnit || "",
      period: kpi?.period || "",
      description: kpi?.description || "",
    },
  })

  async function onSubmit(data: KPIFormValues) {
    setIsSubmitting(true)
    try {
      const kpiData = {
        employee_id: employeeId,
        kpi_name: data.kpiName,
        kpi_value: data.kpiValue || null,
        kpi_target: data.kpiTarget || null,
        kpi_unit: data.kpiUnit || null,
        period: data.period || null,
        description: data.description || null,
      }

      const result = isEditing
        ? await updateEmployeeKPI(kpi.id, kpiData)
        : await createEmployeeKPI(kpiData)

      if (result.success) {
        toast.success(isEditing ? "KPI updated successfully" : "KPI created successfully")
        onClose()
      } else {
        toast.error("error" in result ? result.error : "Failed to save KPI")
      }
    } catch (error) {
      console.error("Error saving KPI:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={onClose}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <CardTitle>{isEditing ? "Edit KPI" : "Add New KPI"}</CardTitle>
            <CardDescription>
              {isEditing ? "Update the KPI details" : "Create a new key performance indicator"}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="kpiName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>KPI Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Customer Satisfaction Score" {...field} />
                  </FormControl>
                  <FormDescription>
                    A clear, descriptive name for this KPI
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="kpiValue"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current Value</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., 85" {...field} />
                    </FormControl>
                    <FormDescription>
                      The current value of this KPI
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="kpiTarget"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Value</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., 90" {...field} />
                    </FormControl>
                    <FormDescription>
                      The target value to achieve
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="kpiUnit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a unit" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {commonUnits.map((unit) => (
                          <SelectItem key={unit} value={unit}>
                            {unit}
                          </SelectItem>
                        ))}
                        <SelectItem value="custom">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The unit of measurement
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="period"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Period</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a period" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {commonPeriods.map((period) => (
                          <SelectItem key={period} value={period}>
                            {period}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The time period for this KPI
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Describe what this KPI measures and why it's important..."
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Additional context or details about this KPI
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                {isEditing ? "Update KPI" : "Create KPI"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}