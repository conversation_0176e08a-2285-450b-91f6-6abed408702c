'use client'

import React from 'react'
import { useTransition } from 'react'
import { Users, Loader, AlertTriangle, CheckCircle } from 'lucide-react'
import { toast } from 'sonner'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { MultiManagerSelector } from './multi-manager-selector'
import { assignManagersToEmployeeAction } from '@/lib/actions/employees'
import type { Employee, Manager } from '@/lib/types'

interface SelectedManager {
  id: string
  name: string
  isPrimary: boolean
}

interface ManagerAssignmentDialogProps {
  isOpen: boolean
  onClose: () => void
  employee: Employee | null
  managers: Manager[]
  onSuccess?: () => void
}

export function ManagerAssignmentDialog({
  isOpen,
  onClose,
  employee,
  managers,
  onSuccess
}: ManagerAssignmentDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = React.useState<string | null>(null)
  const [selectedManagers, setSelectedManagers] = React.useState<SelectedManager[]>([])

  // Initialize selected managers when dialog opens
  React.useEffect(() => {
    if (isOpen && employee) {
      setSubmitError(null)
      
      // Convert employee's current managers to selected managers format
      const currentManagers: SelectedManager[] = []
      
      // Handle legacy single manager
      if (employee.managerId && employee.managerName) {
        currentManagers.push({
          id: employee.managerId,
          name: employee.managerName,
          isPrimary: true
        })
      }
      
      // Handle multiple managers if available
      if (employee.managers && employee.managers.length > 0) {
        const multiManagers = employee.managers.map(manager => ({
          id: manager.managerId,
          name: manager.managerName || 'Unknown Manager',
          isPrimary: manager.isPrimary
        }))
        setSelectedManagers(multiManagers)
      } else {
        setSelectedManagers(currentManagers)
      }
    }
  }, [isOpen, employee])

  const handleSubmit = () => {
    if (!employee || selectedManagers.length === 0) {
      setSubmitError('Please select at least one manager')
      return
    }

    setSubmitError(null)
    
    startTransition(async () => {
      try {
        const managerIds = selectedManagers.map(m => m.id)
        const primaryManagerId = selectedManagers.find(m => m.isPrimary)?.id

        const result = await assignManagersToEmployeeAction(
          employee.id,
          managerIds,
          primaryManagerId
        )

        if (result.success) {
          toast.success(`Successfully assigned ${selectedManagers.length} manager(s) to ${employee.fullName}`)
          onSuccess?.()
          onClose()
        } else {
          setSubmitError(result.error || 'Failed to assign managers')
        }
      } catch (error) {
        console.error('Error assigning managers:', error)
        setSubmitError('An unexpected error occurred')
      }
    })
  }

  const handleClose = () => {
    if (!isPending) {
      setSelectedManagers([])
      setSubmitError(null)
      onClose()
    }
  }

  const primaryManager = selectedManagers.find(m => m.isPrimary)
  const hasChanges = employee && (
    // Check if managers have changed
    JSON.stringify(selectedManagers.map(m => ({ id: m.id, isPrimary: m.isPrimary })).sort()) !==
    JSON.stringify((employee.managers || []).map(m => ({ id: m.managerId, isPrimary: m.isPrimary })).sort())
  )

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Assign Managers
          </DialogTitle>
          <DialogDescription>
            {employee ? (
              <>Manage reporting relationships for <strong>{employee.fullName}</strong></>
            ) : (
              'Select managers for this employee'
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current Assignment Info */}
          {employee && employee.managers && employee.managers.length > 0 && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Currently reports to {employee.managers.length} manager(s).
                Primary: {employee.managers.find(m => m.isPrimary)?.managerName || 'None set'}
              </AlertDescription>
            </Alert>
          )}

          {/* Manager Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Select Managers
            </label>
            <MultiManagerSelector
              managers={managers}
              selectedManagers={selectedManagers}
              onManagersChange={setSelectedManagers}
              disabled={isPending}
              placeholder="Choose managers for this employee..."
            />
          </div>

          {/* Validation Info */}
          {selectedManagers.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">
                • {selectedManagers.length} manager(s) selected
                {primaryManager && (
                  <> • Primary: {primaryManager.name}</>
                )}
              </div>
              {selectedManagers.length > 1 && !primaryManager && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Please designate a primary manager when multiple managers are assigned.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Error Display */}
          {submitError && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              isPending || 
              selectedManagers.length === 0 || 
              (selectedManagers.length > 1 && !primaryManager) ||
              !hasChanges
            }
          >
            {isPending && <Loader className="mr-2 h-4 w-4 animate-spin" />}
            {isPending ? 'Assigning...' : 'Assign Managers'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
