import { auth } from "@clerk/nextjs/server"
import { db } from "@/lib/db"
import { HRFeedbackDashboard } from "@/components/hr-feedback-dashboard"
import { redirect } from "next/navigation"
import { FeedbackStatistics } from "@/lib/types"

export default async function HRFeedbackPage() {
  const { userId } = await auth()

  if (!userId) {
    redirect('/sign-in')
  }

  // Check if user has HR permissions
  const userRole = await db.getUserRole(userId)
  if (!userRole || !['hr-admin', 'super-admin'].includes(userRole)) {
    redirect('/dashboard')
  }

  // Fetch feedback data with error handling
  let pendingFeedback = []
  let statistics = { total: 0, pending: 0, resolved: 0, escalated: 0 }
  
  try {
    const results = await Promise.all([
      db.getPendingFeedbackForHR(),
      db.getFeedbackStatistics()
    ])
    pendingFeedback = results[0]
    statistics = results[1]
  } catch (error) {
    console.error('Error fetching feedback data:', error)
    // Continue with empty data rather than crashing the page
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex flex-col gap-4">
        <h1 className="text-3xl font-bold">HR Feedback Management</h1>
        <p className="text-muted-foreground">
          Review and manage employee feedback submissions
        </p>
      </div>

      <HRFeedbackDashboard 
        pendingFeedback={pendingFeedback}
        statistics={statistics as unknown as FeedbackStatistics}
      />
    </div>
  )
}
