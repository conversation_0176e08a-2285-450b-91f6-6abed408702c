# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Development Commands

```bash
bun run dev              # Start development server
bun run build            # Build for production
bun run start            # Start production server
bun run lint             # Run ESLint

```

## 🏗️ Architecture Overview

This is an **Employee Appraisal Management System** built as a Next.js application with the following architecture:

### Core Structure
- **Frontend**: Next.js 15 with React 19, using App Router
- **Styling**: Tailwind CSS with shadcn/ui components
- **Data Layer**: Mock data with planned PostgreSQL migration (see `/scripts/` folder)
- **State Management**: React Hook Form for forms, server actions for mutations
- **Authentication**: Prepared for Clerk integration (user IDs in mock data)

### Key Domains
- **Departments**: Organizational units
- **Employees**: Staff members with compensation details (hourly/monthly)
- **Managers**: Users who conduct appraisals (Clerk user IDs)
- **Appraisal Periods**: Time windows for performance reviews
- **Appraisals**: Employee performance evaluations with 5 questions

### Data Flow
- **Mock Data**: `lib/data.ts` contains in-memory mock data and accessor functions
- **Actions**: `lib/actions.ts` handles server-side mutations with cache revalidation
- **Types**: `lib/types.ts` defines TypeScript interfaces for all entities

## 📁 Important File Locations

### Core Logic
- `lib/types.ts` - All TypeScript type definitions
- `lib/data.ts` - Mock data store and data access functions
- `lib/actions.ts` - Server actions for data mutations

### Database
- `scripts/01-schema.sql` - PostgreSQL schema definition
- `scripts/02-seed.sql` - Seed data for database
- `scripts/setup-fts-indexes.ts` - Full-Text Search index initialization



### Components
- `components/ui/` - shadcn/ui base components
- `components/*-table.tsx` - Data table components for each entity
- `components/*-form-dialog.tsx` - Modal forms for CRUD operations
- `components/appraisal-form.tsx` - Main appraisal form with auto-save

### Pages
- `app/dashboard/` - Main application dashboard
- `app/dashboard/employees/` - Employee management
- `app/dashboard/departments/` - Department management
- `app/dashboard/periods/` - Appraisal period management
- `app/dashboard/appraisal/[employeeId]/` - Individual appraisal form
- `app/dashboard/approvals/` - Accounting/HR view of appraisals

## 🎨 UI Framework

### Design System
- **Base**: shadcn/ui components with Radix UI primitives
- **Styling**: Tailwind CSS with CSS variables for theming
- **Icons**: Lucide React
- **Tables**: TanStack Table for data grids
- **Forms**: React Hook Form with Zod validation
- **Notifications**: Sonner for toast messages

### Key Patterns
- Modal dialogs for CRUD operations
- Data tables with sorting and filtering
- Auto-save functionality on appraisal forms
- Server actions with optimistic updates
- Responsive sidebar layout

## 🔧 Configuration Notes

### Next.js Config (`next.config.mjs`)
- **Build Errors**: TypeScript and ESLint errors ignored during builds
- **Images**: Unoptimized (likely for deployment compatibility)

### TypeScript
- **Strict Mode**: Enabled
- **Path Mapping**: `@/*` points to root directory
- **Target**: ES6 with modern module resolution

### Development Workflow
1. Mock data is used for all operations
2. Server actions simulate network delays
3. Cache revalidation ensures UI updates
4. Database schema ready for migration from mock data

## 🗄️ Database Schema

The application is designed to migrate from mock data to PostgreSQL:
- **Enums**: `appraisal_status`, `user_role`
- **Tables**: departments, employees, managers, user_roles, employee_assignments, appraisal_periods, appraisals
- **Relationships**: Proper foreign keys with UUID primary keys
- **Authentication**: Designed for Clerk user IDs as manager identifiers



## 🚨 Important Notes

- **Mock Data**: All data is in-memory and resets on server restart
- **Authentication**: Hardcoded mock users, ready for Clerk integration
- **Validation**: Basic client-side validation, expand with Zod schemas
- **Error Handling**: Minimal error boundaries, should be enhanced
- **Security**: No authentication/authorization implemented yet