// Shared utilities and error handling for server actions
// This file exports utility classes and functions used across action modules
// Note: Server-only imports like revalidatePath are imported directly in each action module

// Error classes for better error handling
export class AuthenticationError extends Error {
  constructor(message = "Authentication required") {
    super(message)
    this.name = "AuthenticationError"
  }
}

export class AuthorizationError extends Error {
  constructor(message = "Insufficient permissions") {
    super(message)
    this.name = "AuthorizationError"
  }
}

export class ValidationError extends Error {
  constructor(message = "Validation failed") {
    super(message)
    this.name = "ValidationError"
  }
}

export class RateLimitError extends Error {
  constructor(message = "Rate limit exceeded") {
    super(message)
    this.name = "RateLimitError"
  }
}

// Helper function to handle server action errors
export function handleServerActionError(error: unknown) {
  console.error("Server action error:", error)

  if (error instanceof AuthenticationError) {
    return { success: false, error: "Authentication required", code: "AUTH_REQUIRED" }
  }

  if (error instanceof AuthorizationError) {
    return { success: false, error: "Insufficient permissions", code: "INSUFFICIENT_PERMISSIONS" }
  }

  if (error instanceof ValidationError) {
    return { success: false, error: error.message, code: "VALIDATION_ERROR" }
  }

  if (error instanceof RateLimitError) {
    return { success: false, error: "Too many requests. Please try again later.", code: "RATE_LIMIT" }
  }

  // Generic error for unexpected issues
  return {
    success: false,
    error: "An unexpected error occurred. Please try again.",
    code: "INTERNAL_ERROR"
  }
}

// Note: Common validation and auth helpers are imported directly in each action module
// to avoid server-only import issues with client-side components