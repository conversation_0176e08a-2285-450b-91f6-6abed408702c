"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Footer, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { AlertTriangle, Loader } from "lucide-react"
import type { Department } from "@/lib/types"
import { departmentFormSchema, type Department as DepartmentSchema } from "@/lib/schemas"
import { saveDepartmentAction } from "@/lib/actions"
import { useTransition } from "react"

interface DepartmentFormDialogProps {
  isOpen: boolean
  onClose: () => void
  department: Department | null
}

type DepartmentFormData = Omit<DepartmentSchema, 'id'>

export function DepartmentFormDialog({ isOpen, onClose, department }: DepartmentFormDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = React.useState<string | null>(null)

  const form = useForm<DepartmentFormData>({
    resolver: zodResolver(departmentFormSchema),
    defaultValues: {
      name: "",
    },
  })

  // Reset form when dialog opens/closes or department changes
  React.useEffect(() => {
    if (isOpen) {
      setSubmitError(null)
      form.reset({
        name: department?.name || "",
      })
    }
  }, [department, isOpen, form])

  const onSubmit = async (data: DepartmentFormData) => {
    setSubmitError(null)

    startTransition(async () => {
      try {
        // Create FormData for server action
        const formData = new FormData()
        if (department?.id) {
          formData.append('id', department.id)
        }
        formData.append('name', data.name)

        const result = await saveDepartmentAction(formData)

        if (result.success) {
          onClose()
        } else {
          setSubmitError('error' in result ? result.error : 'Failed to save department')
        }
      } catch (error) {
        console.error('Form submission error:', error)
        setSubmitError('An unexpected error occurred. Please try again.')
      }
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{department ? "Edit Department" : "Add New Department"}</DialogTitle>
        </DialogHeader>

        {submitError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g. Human Resources, Engineering, Marketing"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isPending || !form.formState.isValid}
              >
                {isPending ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  department ? "Update Department" : "Create Department"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
