import { NextRequest, NextResponse } from 'next/server'
import { notificationService } from '@/lib/services/notifications'
import { getCurrentUser } from '@/lib/auth'

/**
 * GET - Fetch notification preferences for current user
 */
export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const preferences = await notificationService.getNotificationPreferences(currentUser.id)
    
    return NextResponse.json({ 
      success: true, 
      preferences 
    })

  } catch (error) {
    console.error('Error fetching notification preferences:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch notification preferences' 
    }, { status: 500 })
  }
}

/**
 * POST - Update notification preferences for current user
 */
export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { email_enabled, reminder_days_before, reminder_time } = body

    // Validate input
    if (typeof email_enabled !== 'boolean') {
      return NextResponse.json({ error: 'email_enabled must be a boolean' }, { status: 400 })
    }

    // Check for undefined/null explicitly instead of truthy check to reject 0
    if (reminder_days_before !== undefined && reminder_days_before !== null) {
      if (reminder_days_before < 1 || reminder_days_before > 14) {
        return NextResponse.json({ error: 'reminder_days_before must be between 1 and 14' }, { status: 400 })
      }
    }

    if (reminder_time !== undefined && reminder_time !== null) {
      // First check format
      if (!/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/.test(reminder_time)) {
        return NextResponse.json({ error: 'reminder_time must be in HH:MM:SS format' }, { status: 400 })
      }
      
      // Then check business hours (08:00:00 to 18:00:00)
      const [hours] = reminder_time.split(':').map(Number)
      if (hours < 8 || hours >= 18) {
        return NextResponse.json({ error: 'reminder_time must be between 08:00:00 and 18:00:00 (business hours)' }, { status: 400 })
      }
    }

    // Wrap service call in try-catch for better error handling
    try {
      const success = await notificationService.updateNotificationPreferences(currentUser.id, {
        email_enabled,
        reminder_days_before,
        reminder_time
      })

      if (success) {
        return NextResponse.json({ 
          success: true, 
          message: 'Notification preferences updated successfully' 
        })
      } else {
        return NextResponse.json({ 
          error: 'Failed to update notification preferences' 
        }, { status: 500 })
      }
    } catch (serviceError) {
      console.error('Service error updating notification preferences:', serviceError)
      return NextResponse.json({ 
        error: serviceError instanceof Error ? serviceError.message : 'Failed to update notification preferences' 
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error updating notification preferences:', error)
    return NextResponse.json({ 
      error: 'Failed to update notification preferences' 
    }, { status: 500 })
  }
}
