"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { CalendarDays, Clock, CheckCircle, AlertCircle } from "lucide-react"
import type { PTOBalance } from "@/lib/types"

interface PTOBalanceCardProps {
  balance: PTOBalance
  className?: string
}

export function PTOBalanceCard({ balance, className }: PTOBalanceCardProps) {
  const usagePercentage = balance.totalDays > 0 ? (balance.usedDays / balance.totalDays) * 100 : 0
  const isLowBalance = balance.availableDays <= 2
  const isNoBalance = balance.availableDays === 0

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <CalendarDays className="h-5 w-5" />
          PTO Balance
        </CardTitle>
        <CardDescription>
          Your paid time off balance for {balance.year}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Balance Summary */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{balance.totalDays}</div>
            <div className="text-sm text-muted-foreground">Total Days</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{balance.usedDays}</div>
            <div className="text-sm text-muted-foreground">Used Days</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              isNoBalance ? 'text-red-600' : 
              isLowBalance ? 'text-yellow-600' : 
              'text-green-600'
            }`}>
              {balance.availableDays}
            </div>
            <div className="text-sm text-muted-foreground">Available Days</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Usage</span>
            <span>{usagePercentage.toFixed(0)}%</span>
          </div>
          <Progress 
            value={usagePercentage} 
            className="h-2"
          />
        </div>

        {/* Status Badge */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {isNoBalance ? (
              <>
                <AlertCircle className="h-4 w-4 text-red-500" />
                <Badge variant="destructive">No Days Available</Badge>
              </>
            ) : isLowBalance ? (
              <>
                <Clock className="h-4 w-4 text-yellow-500" />
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  Low Balance
                </Badge>
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 text-green-500" />
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Good Balance
                </Badge>
              </>
            )}
          </div>
        </div>

        {/* Balance Details */}
        <div className="pt-2 border-t text-xs text-muted-foreground">
          <div className="flex justify-between">
            <span>Last updated:</span>
            <span>
              {new Date(balance.updatedAt).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
              })}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface PTOBalanceSkeletonProps {
  className?: string
}

export function PTOBalanceSkeleton({ className }: PTOBalanceSkeletonProps) {
  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <CalendarDays className="h-5 w-5" />
          PTO Balance
        </CardTitle>
        <CardDescription>
          Loading your PTO balance...
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="h-8 w-8 bg-gray-200 rounded mx-auto animate-pulse" />
            <div className="text-sm text-muted-foreground mt-1">Total Days</div>
          </div>
          <div className="text-center">
            <div className="h-8 w-8 bg-gray-200 rounded mx-auto animate-pulse" />
            <div className="text-sm text-muted-foreground mt-1">Used Days</div>
          </div>
          <div className="text-center">
            <div className="h-8 w-8 bg-gray-200 rounded mx-auto animate-pulse" />
            <div className="text-sm text-muted-foreground mt-1">Available Days</div>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Usage</span>
            <div className="h-4 w-8 bg-gray-200 rounded animate-pulse" />
          </div>
          <div className="h-2 bg-gray-200 rounded animate-pulse" />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="h-6 w-24 bg-gray-200 rounded animate-pulse" />
        </div>
        
        <div className="pt-2 border-t">
          <div className="flex justify-between">
            <span className="text-xs text-muted-foreground">Last updated:</span>
            <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}