# Email Notification System

## Overview

The email notification system automatically sends reminders to managers about pending employee appraisals. By default, managers receive email notifications 4 days before the end of the month for any employees they haven't appraised yet.

## Features

- **Automated Reminders**: Daily cron job checks for due notifications
- **Customizable Timing**: Managers can configure when they receive reminders
- **Smart Scheduling**: Only sends reminders when there are actually pending appraisals
- **Email Templates**: Professional HTML email templates with employee lists
- **Audit Trail**: Complete log of all sent notifications
- **Test Functionality**: Managers can send test emails to verify configuration

## Setup Instructions

### 1. Environment Variables

Add the following to your `.env.local` file:

```bash
# Email Configuration (Resend)
RESEND_API_KEY="re_your_resend_api_key_here"
FROM_EMAIL="<EMAIL>"
TEST_EMAIL="<EMAIL>"

# Cron Job Security
CRON_SECRET="your-secure-cron-secret-key-here"
ADMIN_SECRET="your-secure-admin-secret-key-here"

# Notification Settings
NOTIFICATION_ENABLED="true"
DEFAULT_REMINDER_DAYS="4"
```

### 2. Database Migration

Run the notification system migration:

```bash
# Apply the notification system schema
psql -d your_database -f scripts/add-notification-system.sql
```

### 3. Install Dependencies

```bash
npm install resend
```

### 4. Resend Setup

1. Sign up for [Resend](https://resend.com)
2. Verify your sending domain
3. Get your API key from the dashboard
4. Add the API key to your environment variables

### 5. Cron Job Configuration

#### Vercel (Recommended)
The `vercel.json` file is already configured to run the cron job daily at 9 AM UTC:

```json
{
  "crons": [
    {
      "path": "/api/cron/notifications",
      "schedule": "0 9 * * *"
    }
  ]
}
```

#### Alternative: GitHub Actions
Create `.github/workflows/notifications.yml`:

```yaml
name: Daily Notification Processing
on:
  schedule:
    - cron: '0 9 * * *'  # Daily at 9 AM UTC
  workflow_dispatch:  # Allow manual trigger

jobs:
  process-notifications:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Notification Processing
        run: |
          curl -X GET "${{ secrets.APP_URL }}/api/cron/notifications" \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}"
```

## Usage

### Manager Notification Preferences

Managers can configure their notification preferences in the dashboard:

1. Go to Settings → Notifications
2. Toggle email notifications on/off
3. Set how many days before deadline to receive reminders (1-14 days)
4. Choose time of day for reminders (8 AM - 5 PM)
5. Test email configuration

### Manual Operations

#### Schedule Reminders for Current Period
```bash
curl -X POST "https://yourapp.com/api/cron/notifications" \
  -H "Authorization: Bearer YOUR_ADMIN_SECRET" \
  -H "Content-Type: application/json" \
  -d '{"action": "schedule_reminders"}'
```

#### Process Due Notifications
```bash
curl -X POST "https://yourapp.com/api/cron/notifications" \
  -H "Authorization: Bearer YOUR_ADMIN_SECRET" \
  -H "Content-Type: application/json" \
  -d '{"action": "process_notifications"}'
```

#### Send Immediate Test Notification
```bash
curl -X POST "https://yourapp.com/api/cron/notifications" \
  -H "Authorization: Bearer YOUR_ADMIN_SECRET" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "send_immediate",
    "managerId": "user_manager_id",
    "periodId": "period_uuid"
  }'
```

## Database Schema

### Tables Created

- `appy_notification_preferences`: User notification settings
- `appy_notification_log`: Audit trail of sent notifications
- `appy_scheduled_notifications`: Queue of notifications to be sent

### Key Functions

- `get_pending_appraisals_for_manager()`: Returns employees with pending appraisals
- `schedule_monthly_reminders()`: Creates notification schedule for current period
- `process_scheduled_notifications()`: Processes and sends due notifications

## Email Template

The system sends professional HTML emails with:

- Manager's name personalization
- List of employees with pending appraisals
- Department information for each employee
- Direct link to the appraisal dashboard
- Days remaining until deadline
- Professional styling and branding

## Monitoring and Troubleshooting

### Check Notification Log
```sql
SELECT * FROM appy_notification_log 
WHERE user_id = 'manager_user_id' 
ORDER BY created_at DESC;
```

### Check Scheduled Notifications
```sql
SELECT * FROM appy_scheduled_notifications 
WHERE processed = false 
ORDER BY scheduled_for;
```

### Check Email Delivery Status
```sql
SELECT 
  user_id,
  notification_type,
  email_sent,
  email_error,
  created_at
FROM appy_notification_log 
WHERE email_sent = false 
AND email_error IS NOT NULL;
```

### Common Issues

1. **Emails not sending**: Check Resend API key and domain verification
2. **Cron not running**: Verify Vercel cron configuration and deployment
3. **No notifications scheduled**: Ensure active appraisal period exists
4. **Wrong timing**: Check user notification preferences and timezone settings

## Security Considerations

- Cron endpoints are protected with secret tokens
- Email content doesn't include sensitive salary information
- Notification preferences are user-specific
- All email sends are logged for audit purposes

## Performance

- Cron job typically completes in under 30 seconds
- Batch processing of notifications for efficiency
- Database indexes on key lookup fields
- Email sending is asynchronous and non-blocking
