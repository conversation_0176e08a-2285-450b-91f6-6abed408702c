"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Bell, Clock, Mail, Settings, TestTube } from "lucide-react"
import { toast } from "sonner"

interface NotificationPreferences {
  user_id: string
  email_enabled: boolean
  reminder_days_before: number
  reminder_time: string
}

interface NotificationPreferencesProps {
  userId: string
  initialPreferences?: NotificationPreferences
}

export function NotificationPreferences({ userId, initialPreferences }: NotificationPreferencesProps) {
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    user_id: userId,
    email_enabled: true,
    reminder_days_before: 4,
    reminder_time: '09:00:00',
    ...initialPreferences
  })
  const [isLoading, setIsLoading] = useState(false)
  const [isTesting, setIsTesting] = useState(false)

  const handleSave = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/notifications/preferences', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(preferences)
      })

      if (response.ok) {
        toast.success('Notification preferences saved successfully')
      } else {
        toast.error('Failed to save notification preferences')
      }
    } catch (error) {
      console.error('Error saving preferences:', error)
      toast.error('An error occurred while saving preferences')
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestNotification = async () => {
    setIsTesting(true)
    try {
      const response = await fetch('/api/notifications/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      })

      if (response.ok) {
        toast.success('Test notification sent! Check your email.')
      } else {
        toast.error('Failed to send test notification')
      }
    } catch (error) {
      console.error('Error sending test notification:', error)
      toast.error('An error occurred while sending test notification')
    } finally {
      setIsTesting(false)
    }
  }

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'PM' : 'AM'
    const displayHour = hour % 12 || 12
    return `${displayHour}:${minutes} ${ampm}`
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Notification Preferences
        </CardTitle>
        <CardDescription>
          Configure how and when you receive appraisal reminders
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Email Notifications Toggle */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-base font-medium">Email Notifications</Label>
            <p className="text-sm text-muted-foreground">
              Receive email reminders for pending appraisals
            </p>
          </div>
          <Switch
            checked={preferences.email_enabled}
            onCheckedChange={(checked) =>
              setPreferences(prev => ({ ...prev, email_enabled: checked }))
            }
          />
        </div>

        <Separator />

        {/* Reminder Timing */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <Label className="text-base font-medium">Reminder Timing</Label>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {/* Days Before */}
            <div className="space-y-2">
              <Label htmlFor="reminder-days">Days Before Deadline</Label>
              <Select
                value={preferences.reminder_days_before.toString()}
                onValueChange={(value) =>
                  setPreferences(prev => ({ ...prev, reminder_days_before: parseInt(value) }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 day before</SelectItem>
                  <SelectItem value="2">2 days before</SelectItem>
                  <SelectItem value="3">3 days before</SelectItem>
                  <SelectItem value="4">4 days before</SelectItem>
                  <SelectItem value="5">5 days before</SelectItem>
                  <SelectItem value="7">1 week before</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Time of Day */}
            <div className="space-y-2">
              <Label htmlFor="reminder-time">Time of Day</Label>
              <Select
                value={preferences.reminder_time}
                onValueChange={(value) =>
                  setPreferences(prev => ({ ...prev, reminder_time: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="08:00:00">8:00 AM</SelectItem>
                  <SelectItem value="09:00:00">9:00 AM</SelectItem>
                  <SelectItem value="10:00:00">10:00 AM</SelectItem>
                  <SelectItem value="11:00:00">11:00 AM</SelectItem>
                  <SelectItem value="12:00:00">12:00 PM</SelectItem>
                  <SelectItem value="13:00:00">1:00 PM</SelectItem>
                  <SelectItem value="14:00:00">2:00 PM</SelectItem>
                  <SelectItem value="15:00:00">3:00 PM</SelectItem>
                  <SelectItem value="16:00:00">4:00 PM</SelectItem>
                  <SelectItem value="17:00:00">5:00 PM</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Preview */}
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm">
              <strong>Preview:</strong> You'll receive email reminders{' '}
              <Badge variant="outline">{preferences.reminder_days_before} days before</Badge>{' '}
              the appraisal deadline at{' '}
              <Badge variant="outline">{formatTime(preferences.reminder_time)}</Badge>
            </p>
          </div>
        </div>

        <Separator />

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button 
            onClick={handleSave} 
            disabled={isLoading}
            className="flex-1"
          >
            <Settings className="h-4 w-4 mr-2" />
            {isLoading ? 'Saving...' : 'Save Preferences'}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleTestNotification}
            disabled={isTesting || !preferences.email_enabled}
            className="flex-1"
          >
            <TestTube className="h-4 w-4 mr-2" />
            {isTesting ? 'Sending...' : 'Send Test Email'}
          </Button>
        </div>

        {!preferences.email_enabled && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              <Mail className="h-4 w-4 inline mr-1" />
              Email notifications are disabled. You won't receive appraisal reminders.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
