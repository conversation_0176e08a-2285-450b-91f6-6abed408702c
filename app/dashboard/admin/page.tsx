import { requireRole, getCurrentUser } from '@/lib/auth'
import { redirect } from 'next/navigation'

export default async function AdminRedirectPage() {
  console.log('[ADMIN REDIRECT] Accessing /dashboard/admin - redirecting to user-specific admin page')

  // Require admin or super-admin role
  const currentUser = await requireRole(['super-admin', 'admin'])

  console.log('[ADMIN REDIRECT] Current user:', {
    fullName: currentUser.fullName,
    role: currentUser.role,
    id: currentUser.id,
    email: currentUser.email
  })

  // Redirect to the user's own admin dashboard using their synced ID
  redirect(`/dashboard/admin/${currentUser.id}`)
}
