'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Clock, Save, Loader2, Calendar, Info } from 'lucide-react'

interface EmailSettingValue {
  value: string | number | boolean
  type: 'string' | 'number' | 'boolean'
}

interface EmailSettings {
  default_reminder_days?: EmailSettingValue
  [key: string]: EmailSettingValue | undefined
}

interface Props {
  settings: EmailSettings
  onSave: (settings: EmailSettings) => void
  saving: boolean
}

export function EmailScheduleSettings({ settings, onSave, saving }: Props) {
  const [formData, setFormData] = useState({
    default_reminder_days: settings.default_reminder_days?.value || 4,
    cron_schedule: '0 9 * * *', // Read-only, from env
    timezone: 'UTC', // Read-only, from env
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const settingsUpdate = {
      default_reminder_days: { value: formData.default_reminder_days, type: 'number' as const }
    }
    
    onSave(settingsUpdate)
  }

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Parse cron expression for display
  const parseCronExpression = (cron: string): string => {
    if (!cron || typeof cron !== 'string') {
      return 'Invalid cron expression'
    }
    
    const parts = cron.trim().split(/\s+/)
    if (parts.length < 5) {
      return 'Invalid cron expression format'
    }
    
    const [minute, hour, day, month, weekday, ...rest] = parts
    
    // Basic validation
    const isValidNumber = (val: string, min: number, max: number): boolean => {
      if (val === '*') return true
      const num = parseInt(val, 10)
      return !isNaN(num) && num >= min && num <= max
    }
    
    if (!isValidNumber(minute, 0, 59) || !isValidNumber(hour, 0, 23)) {
      return 'Invalid time in cron expression'
    }
    
    if (day === '*' && month === '*' && weekday === '*') {
      const hourPadded = hour === '*' ? '**' : hour.padStart(2, '0')
      const minutePadded = minute === '*' ? '**' : minute.padStart(2, '0')
      return `Daily at ${hourPadded}:${minutePadded} UTC`
    }
    
    return cron
  }

  const reminderDaysOptions = [
    { value: 1, label: '1 day before' },
    { value: 2, label: '2 days before' },
    { value: 3, label: '3 days before' },
    { value: 4, label: '4 days before' },
    { value: 5, label: '5 days before' },
    { value: 7, label: '1 week before' },
    { value: 10, label: '10 days before' },
    { value: 14, label: '2 weeks before' },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Email Scheduling Settings
        </CardTitle>
        <CardDescription>
          Configure when and how often email reminders are sent
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Cron Schedule (Read-only) */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold">Automated Schedule</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Cron Schedule</Label>
                <div className="p-3 bg-muted rounded-md text-sm font-mono">
                  {formData.cron_schedule}
                </div>
                <p className="text-xs text-muted-foreground">
                  {parseCronExpression(formData.cron_schedule)}
                </p>
              </div>
              
              <div className="space-y-2">
                <Label>Timezone</Label>
                <div className="p-3 bg-muted rounded-md text-sm">
                  {formData.timezone}
                </div>
                <p className="text-xs text-muted-foreground">
                  All scheduled emails run in this timezone
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-2 p-3 bg-blue-50 rounded-md">
              <Info className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium">Note:</p>
                <p>
                  Cron schedule and timezone are configured via environment variables 
                  and cannot be changed from this interface. Contact your system administrator 
                  to modify these settings.
                </p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Reminder Settings */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Reminder Settings</h3>
              <p className="text-sm text-muted-foreground">
                Configure default reminder timing for all managers
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="default_reminder_days">Default Reminder Days</Label>
              <Select 
                value={formData.default_reminder_days.toString()} 
                onValueChange={(value) => handleInputChange('default_reminder_days', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select reminder timing" />
                </SelectTrigger>
                <SelectContent>
                  {reminderDaysOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Default number of days before period end to send reminder emails. 
                Individual managers can override this in their notification preferences.
              </p>
            </div>
            
            <div className="space-y-2">
              <Label>How it works</Label>
              <div className="p-3 bg-muted rounded-md text-sm space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Step 1</Badge>
                  <span>Daily cron job runs at {parseCronExpression(formData.cron_schedule)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Step 2</Badge>
                  <span>System checks for managers with pending appraisals</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Step 3</Badge>
                  <span>Sends reminders {formData.default_reminder_days} days before period end</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Step 4</Badge>
                  <span>Only sends if notifications are enabled and manager has pending work</span>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Save Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Schedule Settings
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}