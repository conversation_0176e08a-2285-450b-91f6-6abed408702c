"use server"

import { revalidatePath } from "next/cache"
import {
  requirePermission,
  checkRateLimit,
  logUserAction,
  validateSession
} from "../auth"
import {
  handleServerActionError,
  RateLimitError,
  ValidationError
} from "./shared"

export async function approveAppraisalAction(appraisalId: string) {
  try {
    // Authentication and authorization checks
    const session = await validateSession()
    
    // Rate limiting
    if (!checkRateLimit(session.userId, 'appraisal-approve', 5, 60000)) {
      throw new RateLimitError()
    }

    // Check if user has approval permissions
    await requirePermission('appraisal:approve')

    const { approveAppraisal } = await import('../data/index')
    const success = await approveAppraisal(appraisalId, session.userId)

    if (!success) {
      throw new Error('Failed to approve appraisal')
    }

    // Log the action
    await logUserAction('appraisal:approve', {
      appraisalId,
      approvedBy: session.userId
    })

    // Revalidate relevant pages
    revalidatePath("/dashboard")
    revalidatePath("/dashboard/approvals")

    return {
      success: true,
      message: "Appraisal approved successfully"
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function rejectAppraisalAction(appraisalId: string, reason: string) {
  try {
    // Authentication and authorization checks
    const session = await validateSession()
    
    // Rate limiting
    if (!checkRateLimit(session.userId, 'appraisal-reject', 5, 60000)) {
      throw new RateLimitError()
    }

    // Check if user has approval permissions
    await requirePermission('appraisal:approve')

    // Validate rejection reason
    if (!reason || reason.trim().length < 10) {
      throw new ValidationError('Rejection reason must be at least 10 characters')
    }

    const { rejectAppraisal } = await import('../data/index')
    const success = await rejectAppraisal(appraisalId, session.userId, reason)

    if (!success) {
      throw new Error('Failed to reject appraisal')
    }

    // Log the action
    await logUserAction('appraisal:reject', {
      appraisalId,
      rejectedBy: session.userId,
      reason
    })

    // Revalidate relevant pages
    revalidatePath("/dashboard")
    revalidatePath("/dashboard/approvals")

    return {
      success: true,
      message: "Appraisal rejected successfully"
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}