"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { debounce } from "lodash"

type AutosaveStatus = "idle" | "saving" | "success" | "error" | "retrying"

interface AutosaveOptions<T> {
  data: T
  onSave: (data: T, signal?: AbortSignal) => Promise<any>
  interval?: number
  maxRetries?: number
  retryDelay?: number
  enabled?: boolean
  onError?: (error: Error, retryCount: number) => void
  onSuccess?: () => void
}

export function useAutosave<T>({
  data,
  onSave,
  interval = 2000,
  maxRetries = 3,
  retryDelay = 5000,
  enabled = true,
  onError,
  onSuccess,
}: AutosaveOptions<T>) {
  const [status, setStatus] = useState<AutosaveStatus>("idle")
  const [retryCount, setRetryCount] = useState(0)
  const [lastSavedData, setLastSavedData] = useState<T | null>(null)

  // Refs for cleanup and cancellation
  const abortControllerRef = useRef<AbortController | null>(null)
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const initialRenderRef = useRef(true)
  const isUnmountedRef = useRef(false)

  // Safe state setter that checks if component is still mounted
  const safeSetStatus = useCallback((newStatus: AutosaveStatus) => {
    if (!isUnmountedRef.current) {
      setStatus(newStatus)
    }
  }, [])

  const safeSetRetryCount = useCallback((count: number) => {
    if (!isUnmountedRef.current) {
      setRetryCount(count)
    }
  }, [])

  // Check if data has actually changed
  const hasDataChanged = useCallback((newData: T, oldData: T | null) => {
    if (oldData === null) return true
    return JSON.stringify(newData) !== JSON.stringify(oldData)
  }, [])

  // Main save function with retry logic
  const saveData = useCallback(async (dataToSave: T, attempt = 0): Promise<void> => {
    if (!enabled || isUnmountedRef.current) return

    // Cancel any previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // Create new abort controller for this request
    abortControllerRef.current = new AbortController()
    const signal = abortControllerRef.current.signal

    try {
      safeSetStatus(attempt > 0 ? "retrying" : "saving")
      safeSetRetryCount(attempt)

      // Call the save function with abort signal
      await onSave(dataToSave, signal)

      // Check if request was aborted
      if (signal.aborted) {
        return
      }

      // Success
      safeSetStatus("success")
      safeSetRetryCount(0)
      setLastSavedData(dataToSave)
      onSuccess?.()

      // Reset to idle after showing success briefly
      setTimeout(() => {
        if (!isUnmountedRef.current) {
          safeSetStatus("idle")
        }
      }, 2000)

    } catch (error) {
      // Check if request was aborted
      if (signal.aborted) {
        return
      }

      console.error(`Autosave attempt ${attempt + 1} failed:`, error)

      // Retry logic
      if (attempt < maxRetries) {
        safeSetStatus("retrying")
        onError?.(error as Error, attempt + 1)

        // Schedule retry
        retryTimeoutRef.current = setTimeout(() => {
          if (!isUnmountedRef.current) {
            saveData(dataToSave, attempt + 1)
          }
        }, retryDelay * Math.pow(2, attempt)) // Exponential backoff

      } else {
        // Max retries reached
        safeSetStatus("error")
        safeSetRetryCount(0)
        onError?.(error as Error, attempt + 1)

        // Reset to idle after showing error
        setTimeout(() => {
          if (!isUnmountedRef.current) {
            safeSetStatus("idle")
          }
        }, 5000)
      }
    }
  }, [enabled, onSave, maxRetries, retryDelay, onError, onSuccess, safeSetStatus, safeSetRetryCount])

  // Debounced save function to prevent excessive calls
  const debouncedSave = useCallback(
    debounce((dataToSave: T) => {
      if (hasDataChanged(dataToSave, lastSavedData)) {
        saveData(dataToSave)
      }
    }, interval),
    [saveData, interval, hasDataChanged, lastSavedData]
  )

  // Manual save function (bypasses debouncing)
  const saveNow = useCallback(() => {
    if (hasDataChanged(data, lastSavedData)) {
      debouncedSave.cancel() // Cancel any pending debounced save
      saveData(data)
    }
  }, [data, lastSavedData, hasDataChanged, saveData, debouncedSave])

  // Effect to trigger autosave when data changes
  useEffect(() => {
    if (initialRenderRef.current) {
      initialRenderRef.current = false
      return
    }

    if (!enabled) return

    // Reset status when data changes
    safeSetStatus("idle")

    // Trigger debounced save
    debouncedSave(data)

    return () => {
      // Cleanup is handled in the main cleanup effect
    }
  }, [data, enabled, debouncedSave, safeSetStatus])

  // Cleanup effect
  useEffect(() => {
    return () => {
      isUnmountedRef.current = true

      // Cancel any pending debounced saves
      debouncedSave.cancel()

      // Cancel any ongoing requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Clear retry timeout
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
      }
    }
  }, [debouncedSave])

  // Return status and utility functions
  return {
    status,
    retryCount,
    saveNow,
    isChanged: hasDataChanged(data, lastSavedData),
    lastSavedData,
  }
}
