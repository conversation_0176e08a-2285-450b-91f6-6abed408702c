import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { announceLoadingState } from "@/lib/accessibility"
import { useEffect } from "react"

interface LoadingStateProps {
  context: string
  children?: React.ReactNode
}

export function TableSkeleton({ context }: LoadingStateProps) {
  useEffect(() => {
    console.log('[A11Y] Showing table skeleton for:', context)
    announceLoadingState(true, context)
    
    return () => {
      announceLoadingState(false, context)
    }
  }, [context])

  return (
    <div 
      className="space-y-4"
      role="status"
      aria-label={`Loading ${context}`}
      aria-live="polite"
    >
      {/* Filter and action bar skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-10 w-64" />
        <Skeleton className="h-10 w-32" />
      </div>
      
      {/* Table skeleton */}
      <div className="rounded-md border">
        <div className="p-4">
          {/* Header row */}
          <div className="flex space-x-4 mb-4">
            <Skeleton className="h-6 flex-1" />
            <Skeleton className="h-6 flex-1" />
            <Skeleton className="h-6 flex-1" />
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-6 w-16" />
          </div>
          
          {/* Data rows */}
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex space-x-4 mb-3">
              <Skeleton className="h-8 flex-1" />
              <Skeleton className="h-8 flex-1" />
              <Skeleton className="h-8 flex-1" />
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-16" />
            </div>
          ))}
        </div>
      </div>
      
      {/* Pagination skeleton */}
      <div className="flex justify-end space-x-2">
        <Skeleton className="h-9 w-20" />
        <Skeleton className="h-9 w-16" />
      </div>
      
      <span className="sr-only">Loading {context} data, please wait...</span>
    </div>
  )
}

export function FormSkeleton({ context }: LoadingStateProps) {
  useEffect(() => {
    console.log('[A11Y] Showing form skeleton for:', context)
    announceLoadingState(true, context)
    
    return () => {
      announceLoadingState(false, context)
    }
  }, [context])

  return (
    <Card 
      role="status"
      aria-label={`Loading ${context} form`}
      aria-live="polite"
    >
      <CardHeader>
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-4 w-64" />
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Form fields */}
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
        
        {/* Action buttons */}
        <div className="flex justify-end space-x-2">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-24" />
        </div>
      </CardContent>
      
      <span className="sr-only">Loading {context} form, please wait...</span>
    </Card>
  )
}

export function CardSkeleton({ context }: LoadingStateProps) {
  useEffect(() => {
    console.log('[A11Y] Showing card skeleton for:', context)
    announceLoadingState(true, context)
    
    return () => {
      announceLoadingState(false, context)
    }
  }, [context])

  return (
    <Card 
      role="status"
      aria-label={`Loading ${context}`}
      aria-live="polite"
    >
      <CardHeader>
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-4 w-48" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </CardContent>
      
      <span className="sr-only">Loading {context}, please wait...</span>
    </Card>
  )
}

export function DashboardSkeleton() {
  useEffect(() => {
    console.log('[A11Y] Showing dashboard skeleton')
    announceLoadingState(true, "dashboard")
    
    return () => {
      announceLoadingState(false, "dashboard")
    }
  }, [])

  return (
    <div 
      className="space-y-6"
      role="status"
      aria-label="Loading dashboard"
      aria-live="polite"
    >
      {/* Header */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-64" />
      </div>
      
      {/* Stats cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
      
      {/* Main content area */}
      <div className="grid gap-6 md:grid-cols-2">
        <CardSkeleton context="recent activity" />
        <CardSkeleton context="pending tasks" />
      </div>
      
      <span className="sr-only">Loading dashboard content, please wait...</span>
    </div>
  )
}

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  context?: string
  className?: string
}

export function LoadingSpinner({ size = 'md', context, className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  useEffect(() => {
    if (context) {
      console.log('[A11Y] Showing loading spinner for:', context)
      announceLoadingState(true, context)
      
      return () => {
        announceLoadingState(false, context)
      }
    }
  }, [context])

  return (
    <div 
      className={`flex items-center justify-center ${className}`}
      role="status"
      aria-label={context ? `Loading ${context}` : "Loading"}
      aria-live="polite"
    >
      <div 
        className={`animate-spin rounded-full border-2 border-gray-300 border-t-primary ${sizeClasses[size]}`}
        aria-hidden="true"
      />
      <span className="sr-only">
        {context ? `Loading ${context}, please wait...` : "Loading, please wait..."}
      </span>
    </div>
  )
}

interface LoadingOverlayProps {
  isVisible: boolean
  context: string
  children: React.ReactNode
}

export function LoadingOverlay({ isVisible, context, children }: LoadingOverlayProps) {
  useEffect(() => {
    if (isVisible) {
      console.log('[A11Y] Showing loading overlay for:', context)
      announceLoadingState(true, context)
    } else {
      announceLoadingState(false, context)
    }
  }, [isVisible, context])

  if (!isVisible) {
    return <>{children}</>
  }

  return (
    <div className="relative">
      <div className="opacity-50 pointer-events-none">
        {children}
      </div>
      <div 
        className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm"
        role="status"
        aria-label={`Loading ${context}`}
        aria-live="polite"
      >
        <LoadingSpinner size="lg" context={context} />
      </div>
    </div>
  )
}
