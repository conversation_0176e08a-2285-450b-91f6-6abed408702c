"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/types"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Target,
  Award,
  Plus,
  Calendar
} from "lucide-react"
import { useState } from "react"

interface KPIDashboardProps {
  kpis: EmployeeKPI[]
  canManageKPIs?: boolean
}

export function KPIDashboard({ kpis, canManageKPIs = false }: KPIDashboardProps) {
  const [expandedKPIs, setExpandedKPIs] = useState<Set<string>>(new Set())

  const toggleExpanded = (kpiId: string) => {
    const newExpanded = new Set(expandedKPIs)
    if (newExpanded.has(kpiId)) {
      newExpanded.delete(kpiId)
    } else {
      newExpanded.add(kpiId)
    }
    setExpandedKPIs(newExpanded)
  }

  const calculateProgress = (current: string, target: string): number => {
    const currentNum = parseFloat(current)
    const targetNum = parseFloat(target)
    if (isNaN(currentNum) || isNaN(targetNum) || targetNum === 0) return 0
    return Math.min(100, (currentNum / targetNum) * 100)
  }

  const getPerformanceStatus = (progress: number): {
    color: string
    icon: React.ReactNode
    label: string
  } => {
    if (progress >= 100) {
      return {
        color: "text-green-600 dark:text-green-400",
        icon: <Award className="h-4 w-4" />,
        label: "Exceeded"
      }
    } else if (progress >= 80) {
      return {
        color: "text-green-600 dark:text-green-400",
        icon: <TrendingUp className="h-4 w-4" />,
        label: "On Track"
      }
    } else if (progress >= 60) {
      return {
        color: "text-yellow-600 dark:text-yellow-400",
        icon: <Minus className="h-4 w-4" />,
        label: "At Risk"
      }
    } else {
      return {
        color: "text-red-600 dark:text-red-400",
        icon: <TrendingDown className="h-4 w-4" />,
        label: "Behind"
      }
    }
  }

  const getProgressColor = (progress: number): string => {
    if (progress >= 100) return "bg-green-500"
    if (progress >= 80) return "bg-blue-500"
    if (progress >= 60) return "bg-yellow-500"
    return "bg-red-500"
  }

  if (kpis.length === 0) {
    return (
      <Card>
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 rounded-full bg-muted w-fit">
            <BarChart3 className="h-8 w-8 text-muted-foreground" />
          </div>
          <CardTitle className="text-xl">Key Performance Indicators</CardTitle>
          <CardDescription>
            Track and manage employee KPIs
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="space-y-4">
            <p className="text-muted-foreground">No KPIs have been set up yet.</p>
            {canManageKPIs && (
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                Add First KPI
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-primary/10">
              <BarChart3 className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-xl">Key Performance Indicators</CardTitle>
              <CardDescription>
                {kpis.length} KPI{kpis.length !== 1 ? 's' : ''} tracked
              </CardDescription>
            </div>
          </div>
          {canManageKPIs && (
            <Button variant="outline" size="sm" className="gap-2">
              <Plus className="h-4 w-4" />
              Add KPI
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2">
          {kpis.map((kpi) => {
            const progress = kpi.kpiValue && kpi.kpiTarget 
              ? calculateProgress(kpi.kpiValue, kpi.kpiTarget)
              : 0
            const status = getPerformanceStatus(progress)
            const isExpanded = expandedKPIs.has(kpi.id)

            return (
              <Card 
                key={kpi.id} 
                className="border-2 hover:shadow-md transition-all duration-200 cursor-pointer"
                onClick={() => toggleExpanded(kpi.id)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-base leading-tight">
                        {kpi.kpiName}
                      </h4>
                      {kpi.description && (
                        <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                          {kpi.description}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2 ml-3">
                      {status.icon}
                      {kpi.period && (
                        <Badge variant="outline" className="text-xs whitespace-nowrap">
                          <Calendar className="h-3 w-3 mr-1" />
                          {kpi.period}
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="space-y-4">
                    {/* Performance Status */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`p-1 rounded-full ${status.color}`}>
                          {status.icon}
                        </div>
                        <span className={`text-sm font-medium ${status.color}`}>
                          {status.label}
                        </span>
                      </div>
                      {progress > 0 && (
                        <span className="text-sm font-bold">
                          {progress.toFixed(0)}%
                        </span>
                      )}
                    </div>

                    {/* Progress Bar */}
                    {kpi.kpiValue && kpi.kpiTarget && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Progress</span>
                          <span className="font-medium">
                            {kpi.kpiValue}{kpi.kpiUnit && ` ${kpi.kpiUnit}`} / {kpi.kpiTarget}{kpi.kpiUnit && ` ${kpi.kpiUnit}`}
                          </span>
                        </div>
                        <div className="relative">
                          <Progress 
                            value={progress} 
                            className="h-2"
                          />
                          <div 
                            className={`absolute top-0 left-0 h-2 rounded-full transition-all duration-500 ${getProgressColor(progress)}`}
                            style={{ width: `${Math.min(100, progress)}%` }}
                          />
                        </div>
                      </div>
                    )}

                    {/* Values Display */}
                    {!kpi.kpiTarget && kpi.kpiValue && (
                      <div className="flex items-center justify-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-primary">
                            {kpi.kpiValue}
                            {kpi.kpiUnit && (
                              <span className="text-sm font-normal text-muted-foreground ml-1">
                                {kpi.kpiUnit}
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">Current Value</p>
                        </div>
                      </div>
                    )}

                    {/* Expanded Details */}
                    {isExpanded && (
                      <div className="pt-2 border-t space-y-2 text-sm">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-muted-foreground">Created</p>
                            <p className="font-medium">
                              {new Date(kpi.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Updated</p>
                            <p className="font-medium">
                              {new Date(kpi.updatedAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}