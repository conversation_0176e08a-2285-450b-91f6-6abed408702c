import { NextRequest, NextResponse } from 'next/server'
import { emailAdminService } from '@/lib/services/email-admin'
import { requireRole } from '@/lib/auth'

/**
 * GET - Fetch email settings and system status
 */
export async function GET(request: NextRequest) {
  try {
    // Require super-admin role
    const currentUser = await requireRole(['super-admin'])
    
    // Get all email settings
    const settings = await emailAdminService.getEmailSettings()
    
    // Get system status
    const systemStatus = await emailAdminService.getEmailSystemStatus()
    
    // Get email templates
    const templates = await emailAdminService.getEmailTemplates()
    
    // Convert settings array to object for easier frontend consumption
    const settingsObj = settings.reduce((acc, setting) => {
      let value: any = setting.setting_value
      
      // Parse values based on type
      if (setting.setting_type === 'boolean') {
        value = setting.setting_value === 'true'
      } else if (setting.setting_type === 'number') {
        value = parseFloat(setting.setting_value)
      } else if (setting.setting_type === 'json') {
        try {
          value = JSON.parse(setting.setting_value)
        } catch {
          value = setting.setting_value
        }
      }
      
      acc[setting.setting_key] = {
        id: setting.id,
        value,
        type: setting.setting_type,
        description: setting.description,
        updated_at: setting.updated_at
      }
      
      return acc
    }, {} as Record<string, any>)
    
    return NextResponse.json({
      success: true,
      data: {
        settings: settingsObj,
        templates,
        systemStatus
      }
    })

  } catch (error) {
    console.error('Error fetching email settings:', error)
    
    if (error instanceof Error && error.message.includes('Unauthorized')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    return NextResponse.json({ 
      error: 'Failed to fetch email settings' 
    }, { status: 500 })
  }
}

/**
 * POST - Update email settings
 */
export async function POST(request: NextRequest) {
  try {
    // Require super-admin role
    const currentUser = await requireRole(['super-admin'])
    
    const body = await request.json()
    const { settings, templates } = body
    
    const results = {
      settings: [] as any[],
      templates: [] as any[],
      errors: [] as string[]
    }
    
    // Update settings if provided
    if (settings && typeof settings === 'object') {
      for (const [key, settingData] of Object.entries(settings)) {
        try {
          const { value, type } = settingData as any
          
          // Convert value to string for storage
          let stringValue = value
          if (type === 'boolean') {
            stringValue = value ? 'true' : 'false'
          } else if (type === 'number') {
            stringValue = value.toString()
          } else if (type === 'json') {
            stringValue = JSON.stringify(value)
          }
          
          const updatedSetting = await emailAdminService.updateEmailSetting(
            key, 
            stringValue, 
            currentUser.id,
            type
          )
          
          results.settings.push(updatedSetting)
        } catch (error) {
          console.error(`Error updating setting ${key}:`, error)
          results.errors.push(`Failed to update setting: ${key}`)
        }
      }
    }
    
    // Update templates if provided
    if (templates && Array.isArray(templates)) {
      for (const template of templates) {
        try {
          const { template_key, ...templateData } = template
          
          const updatedTemplate = await emailAdminService.updateEmailTemplate(
            template_key,
            templateData,
            currentUser.id
          )
          
          results.templates.push(updatedTemplate)
        } catch (error) {
          console.error(`Error updating template ${template.template_key}:`, error)
          results.errors.push(`Failed to update template: ${template.template_key}`)
        }
      }
    }
    
    return NextResponse.json({
      success: results.errors.length === 0,
      data: results,
      message: results.errors.length === 0 
        ? 'Email settings updated successfully'
        : `Updated with ${results.errors.length} errors`
    })

  } catch (error) {
    console.error('Error updating email settings:', error)
    
    if (error instanceof Error && error.message.includes('Unauthorized')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    return NextResponse.json({ 
      error: 'Failed to update email settings' 
    }, { status: 500 })
  }
}

/**
 * PUT - Test email configuration
 */
export async function PUT(request: NextRequest) {
  try {
    // Require super-admin role
    const currentUser = await requireRole(['super-admin'])
    
    const body = await request.json()
    const { action } = body
    
    if (action === 'test') {
      const success = await emailAdminService.testEmailConfiguration(currentUser.id)
      
      return NextResponse.json({
        success,
        message: success 
          ? 'Test email sent successfully'
          : 'Failed to send test email'
      })
    }
    
    return NextResponse.json({ 
      error: 'Invalid action' 
    }, { status: 400 })

  } catch (error) {
    console.error('Error testing email configuration:', error)
    
    if (error instanceof Error && error.message.includes('Unauthorized')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Failed to test email configuration'
    }, { status: 500 })
  }
}