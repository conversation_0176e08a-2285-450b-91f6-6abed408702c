import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { db } from '@/lib/db'
import { z } from 'zod'

const submitFeedbackSchema = z.object({
  targetEmployeeId: z.string().uuid().optional().nullable(),
  feedbackType: z.enum(['complaint', 'suggestion', 'recognition', 'concern', 'initiative', 'general']),
  category: z.enum(['performance', 'behavior', 'communication', 'teamwork', 'leadership', 'process', 'other']).optional().nullable(),
  subject: z.string().min(5).max(255),
  message: z.string().min(20).max(2000),
  isAnonymous: z.boolean().default(false),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium')
})

/**
 * POST - Submit new employee feedback
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Processing feedback submission...')

    const { userId } = await auth()
    if (!userId) {
      console.log('❌ Unauthorized feedback submission attempt')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()

    // Validate input
    const validatedData = submitFeedbackSchema.parse(body)

    console.log('📝 Feedback submission data:', {
      userId,
      targetEmployeeId: validatedData.targetEmployeeId,
      feedbackType: validatedData.feedbackType,
      category: validatedData.category,
      subject: validatedData.subject?.substring(0, 50) + '...',
      isAnonymous: validatedData.isAnonymous,
      priority: validatedData.priority
    })

    // Get the employee record for the submitter
    const submitter = await db.getEmployeeByClerkId(userId)
    if (!submitter) {
      console.log('❌ Employee not found for user:', userId)
      return NextResponse.json({
        error: 'Employee record not found. Please contact HR.'
      }, { status: 404 })
    }

    console.log('👤 Submitter found:', submitter.full_name)

    // Validate target employee if provided
    if (validatedData.targetEmployeeId) {
      const targetEmployee = await db.getEmployeeById(validatedData.targetEmployeeId)
      if (!targetEmployee) {
        console.log('❌ Target employee not found:', validatedData.targetEmployeeId)
        return NextResponse.json({
          error: 'Target employee not found'
        }, { status: 404 })
      }
      console.log('🎯 Target employee found:', targetEmployee.full_name)
    }

    // Create the feedback record
    const feedback = await db.createFeedback({
      submitterId: submitter.id,
      targetEmployeeId: validatedData.targetEmployeeId || undefined,
      feedbackType: validatedData.feedbackType,
      category: validatedData.category || undefined,
      subject: validatedData.subject,
      message: validatedData.message,
      isAnonymous: validatedData.isAnonymous,
      priority: validatedData.priority
    })

    console.log('✅ Feedback created successfully:', feedback.id)

    // TODO: Send notification to HR team
    // This could be implemented with email notifications or in-app notifications

    return NextResponse.json({
      success: true,
      feedbackId: feedback.id,
      message: 'Feedback submitted successfully'
    })

  } catch (error) {
    console.error('❌ Error submitting feedback:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Invalid input data',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      error: 'Failed to submit feedback. Please try again.'
    }, { status: 500 })
  }
}
