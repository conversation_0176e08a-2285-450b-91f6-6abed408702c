"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Plus, Edit, Copy, Trash2, Archive } from "lucide-react"
import type { AppraisalTemplate } from "@/lib/types"

interface TemplateManagementProps {
  templates: AppraisalTemplate[]
  onCreateTemplate: () => void
  onEditTemplate: (template: AppraisalTemplate) => void
  onDuplicateTemplate: (template: AppraisalTemplate) => void
  onToggleTemplate: (template: AppraisalTemplate) => void
  onDeleteTemplate: (template: AppraisalTemplate) => void
}

export function TemplateManagement({
  templates,
  onCreateTemplate,
  onEditTemplate,
  onDuplicateTemplate,
  onToggleTemplate,
  onDeleteTemplate
}: TemplateManagementProps) {
  const [filter, setFilter] = useState<'all' | 'active' | 'inactive'>('active')

  const filteredTemplates = templates.filter(template => {
    if (filter === 'active') return template.isActive
    if (filter === 'inactive') return !template.isActive
    return true
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Appraisal Templates</h1>
          <p className="text-muted-foreground">Manage reusable appraisal form templates</p>
        </div>
        <Button onClick={onCreateTemplate}>
          <Plus className="mr-2 h-4 w-4" />
          Create Template
        </Button>
      </div>

      {/* Filter Tabs */}
      <div className="flex space-x-1 rounded-lg bg-muted p-1 w-fit">
        <Button
          variant={filter === 'active' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setFilter('active')}
        >
          Active ({templates.filter(t => t.isActive).length})
        </Button>
        <Button
          variant={filter === 'inactive' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setFilter('inactive')}
        >
          Inactive ({templates.filter(t => !t.isActive).length})
        </Button>
        <Button
          variant={filter === 'all' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setFilter('all')}
        >
          All ({templates.length})
        </Button>
      </div>

      {/* Templates Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className={!template.isActive ? 'opacity-60' : ''}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  {template.description && (
                    <CardDescription>{template.description}</CardDescription>
                  )}
                </div>
                <div className="flex flex-col gap-1">
                  {template.isDefault && (
                    <Badge variant="secondary" className="text-xs">
                      Default
                    </Badge>
                  )}
                  {template.departmentName && (
                    <Badge variant="outline" className="text-xs">
                      {template.departmentName}
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Template Info */}
                <div className="text-sm text-muted-foreground">
                  <p>{template.questions.length} questions • Version {template.version}</p>
                  <p>Created by {template.createdBy}</p>
                </div>

                {/* Question Preview */}
                <div className="space-y-2">
                  <p className="text-sm font-medium">Questions:</p>
                  <div className="space-y-1">
                    {template.questions.slice(0, 3).map((question, index) => (
                      <div key={index} className="text-sm text-muted-foreground">
                        {index + 1}. {question.question}
                        {question.required && <span className="text-red-500 ml-1">*</span>}
                      </div>
                    ))}
                    {template.questions.length > 3 && (
                      <div className="text-sm text-muted-foreground">
                        +{template.questions.length - 3} more questions
                      </div>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => onEditTemplate(template)}
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => onDuplicateTemplate(template)}
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => onToggleTemplate(template)}
                  >
                    <Archive className="h-3 w-3 mr-1" />
                    {template.isActive ? 'Deactivate' : 'Activate'}
                  </Button>
                  <Button 
                    variant="destructive" 
                    size="sm" 
                    onClick={() => onDeleteTemplate(template)}
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <p className="text-muted-foreground mb-4">
              {filter === 'active' ? 'No active templates found' : 
               filter === 'inactive' ? 'No inactive templates found' : 
               'No templates found'}
            </p>
            <Button onClick={onCreateTemplate}>
              <Plus className="mr-2 h-4 w-4" />
              Create Your First Template
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}