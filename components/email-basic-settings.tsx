'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { Mail, User, Save, Loader2, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

// Type definition for email settings
interface EmailSettingValue {
  value: string | boolean
  type: 'string' | 'boolean'
}

interface EmailSettings {
  from_email?: EmailSettingValue
  from_name?: EmailSettingValue
  reply_to_email?: EmailSettingValue
  test_email?: EmailSettingValue
  notifications_enabled?: EmailSettingValue
  email_signature?: EmailSettingValue
}

interface Props {
  settings: EmailSettings
  onSave: (settings: EmailSettings) => void
  saving: boolean
}

// Simple HTML sanitization function for email signatures
function sanitizeHtml(html: string): string {
  // Allow only safe HTML tags and attributes
  const allowedTags = ['br', 'strong', 'em', 'b', 'i', 'u', 'p', 'a', 'ul', 'ol', 'li']
  const allowedAttrs = ['href', 'target']
  
  // Remove script tags and event handlers
  let sanitized = html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
    .replace(/javascript:/gi, '')
  
  // Remove non-allowed tags
  const tagRegex = /<\/?([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>/g
  sanitized = sanitized.replace(tagRegex, (match, tag) => {
    if (allowedTags.includes(tag.toLowerCase())) {
      // For allowed tags, strip non-allowed attributes
      return match.replace(/\s+([a-zA-Z-]+)\s*=\s*["'][^"']*["']/g, (attrMatch, attr) => {
        return allowedAttrs.includes(attr.toLowerCase()) ? attrMatch : ''
      })
    }
    return ''
  })
  
  return sanitized
}

export function EmailBasicSettings({ settings, onSave, saving }: Props) {
  const [formData, setFormData] = useState({
    from_email: (settings.from_email?.value as string) || '',
    from_name: (settings.from_name?.value as string) || '',
    reply_to_email: (settings.reply_to_email?.value as string) || '',
    test_email: (settings.test_email?.value as string) || '',
    notifications_enabled: (settings.notifications_enabled?.value as boolean) || false,
    email_signature: (settings.email_signature?.value as string) || ''
  })
  
  const [signatureWarning, setSignatureWarning] = useState<string | null>(null)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Sanitize email signature before saving
    const sanitizedSignature = sanitizeHtml(formData.email_signature)
    
    const settingsUpdate: EmailSettings = {
      from_email: { value: formData.from_email, type: 'string' },
      from_name: { value: formData.from_name, type: 'string' },
      reply_to_email: { value: formData.reply_to_email, type: 'string' },
      test_email: { value: formData.test_email, type: 'string' },
      notifications_enabled: { value: formData.notifications_enabled, type: 'boolean' },
      email_signature: { value: sanitizedSignature, type: 'string' }
    }
    
    onSave(settingsUpdate)
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Check for potentially unsafe HTML in signature
    if (field === 'email_signature' && typeof value === 'string') {
      const sanitized = sanitizeHtml(value)
      if (sanitized !== value) {
        setSignatureWarning('Some HTML tags or attributes were removed for security. Only basic formatting tags are allowed.')
      } else {
        setSignatureWarning(null)
      }
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Basic Email Settings
        </CardTitle>
        <CardDescription>
          Configure sender information and basic email system settings
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Sender Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold">Sender Information</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="from_email">From Email Address</Label>
                <Input
                  id="from_email"
                  type="email"
                  value={formData.from_email}
                  onChange={(e) => handleInputChange('from_email', e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  This will be the sender address for all system emails
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="from_name">From Name</Label>
                <Input
                  id="from_name"
                  value={formData.from_name}
                  onChange={(e) => handleInputChange('from_name', e.target.value)}
                  placeholder="Company Appraisal System"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Display name shown to email recipients
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="reply_to_email">Reply-To Email</Label>
                <Input
                  id="reply_to_email"
                  type="email"
                  value={formData.reply_to_email}
                  onChange={(e) => handleInputChange('reply_to_email', e.target.value)}
                  placeholder="<EMAIL>"
                />
                <p className="text-xs text-muted-foreground">
                  Email address for replies (optional)
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="test_email">Test Email Address</Label>
                <Input
                  id="test_email"
                  type="email"
                  value={formData.test_email}
                  onChange={(e) => handleInputChange('test_email', e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Email address to receive test emails
                </p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Email Signature */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Email Signature</h3>
              <p className="text-sm text-muted-foreground">
                Optional signature to append to all emails (HTML supported)
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email_signature">Signature</Label>
              <Textarea
                id="email_signature"
                value={formData.email_signature}
                onChange={(e) => handleInputChange('email_signature', e.target.value)}
                placeholder="Best regards,<br>The HR Team"
                rows={3}
                className="font-mono text-sm"
              />
              <p className="text-xs text-muted-foreground">
                Allowed HTML tags: &lt;br&gt;, &lt;strong&gt;, &lt;em&gt;, &lt;b&gt;, &lt;i&gt;, &lt;u&gt;, &lt;p&gt;, &lt;a&gt;, &lt;ul&gt;, &lt;ol&gt;, &lt;li&gt;
              </p>
              {signatureWarning && (
                <Alert className="mt-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{signatureWarning}</AlertDescription>
                </Alert>
              )}
            </div>
          </div>

          <Separator />

          {/* System Settings */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">System Settings</h3>
              <p className="text-sm text-muted-foreground">
                Global email notification settings
              </p>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Email Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Enable or disable all email notifications globally
                </p>
              </div>
              <Switch
                checked={formData.notifications_enabled}
                onCheckedChange={(checked) => handleInputChange('notifications_enabled', checked)}
              />
            </div>
          </div>

          <Separator />

          {/* Save Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Basic Settings
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}