import { notFound } from "next/navigation"
import { EmployeeProfile } from "@/components/employee-profile"
import { EmployeeKPIList } from "@/components/employee-kpi-list"
import { getEmployeeProfile, getEmployeeKPIs } from "@/lib/data/employees"
import { getCurrentUser } from "@/lib/auth"

export default async function EmployeeProfilePage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  const { id } = await params
  const currentUser = await getCurrentUser()
  const employee = await getEmployeeProfile(id)
  
  if (!employee) {
    notFound()
  }

  const kpis = await getEmployeeKPIs(id)
  
  // Check if current user can edit this profile
  const canEdit = 
    currentUser?.id === id || // Employee can edit their own profile
    currentUser?.role === 'hr-admin' ||
    currentUser?.role === 'super-admin' ||
    (currentUser?.role === 'manager' && employee.managerId === currentUser.id)

  // Check if current user can manage KPIs
  const canManageKPIs = 
    currentUser?.role === 'hr-admin' ||
    currentUser?.role === 'super-admin' ||
    (currentUser?.role === 'manager' && employee.managerId === currentUser.id)

  return (
    <div className="min-h-screen bg-muted/30">
      <div className="container max-w-5xl py-6 sm:py-8 px-4 sm:px-6 lg:px-8 mx-auto">
        <EmployeeProfile
          employee={{ ...employee, kpis }}
          canEdit={canEdit}
          currentUserRole={currentUser?.role}
        />
        
        <div className="mt-8">
          <EmployeeKPIList
            employeeId={id}
            kpis={kpis}
            canManage={canManageKPIs}
          />
        </div>
      </div>
    </div>
  )
}